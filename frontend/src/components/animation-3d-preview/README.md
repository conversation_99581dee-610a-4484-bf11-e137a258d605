# 3D Animation Preview Component

A React component for previewing FBX animations using Three.js and @react-three/fiber.

## Features

- **FBX Model Loading**: Supports loading FBX files with embedded animations
- **Animation Controls**: Play, pause, restart, and scrub through animations
- **Interactive Camera**: Orbit controls for 3D navigation
- **Professional Rendering**: Studio lighting and environment mapping
- **Performance Monitoring**: Optional performance statistics display
- **Responsive Design**: Adapts to different screen sizes

## Components

### Animation3DPreview (Main Component)
The main modal component that provides the 3D preview interface.

**Props:**
- `fbxUrl?: string` - URL to the FBX file
- `animationName?: string` - Display name for the animation
- `isOpen: boolean` - Controls modal visibility
- `onClose: () => void` - Callback when modal is closed

### Scene3D
The Three.js scene component that handles 3D rendering.

**Props:**
- `fbxUrl: string` - URL to the FBX file
- `isPlaying: boolean` - Animation playback state
- `progress: number` - Animation progress (0-100)
- `showStats?: boolean` - Whether to show performance stats
- `onLoadComplete: (duration: number) => void` - Callback when model loads
- `onLoadError: (error: string) => void` - Callback when loading fails

### FBXModel
Handles FBX file loading and animation playback.

**Props:**
- `url: string` - URL to the FBX file
- `isPlaying: boolean` - Animation playback state
- `progress: number` - Animation progress (0-100)
- `onLoadComplete: (duration: number) => void` - Callback when model loads
- `onLoadError: (error: string) => void` - Callback when loading fails

## Usage

```tsx
import Animation3DPreview from '@/components/animation-3d-preview';

function MyComponent() {
  const [isOpen, setIsOpen] = useState(false);
  const fbxUrl = 'https://example.com/animation.fbx';

  return (
    <>
      <button onClick={() => setIsOpen(true)}>
        Preview Animation
      </button>
      
      <Animation3DPreview
        fbxUrl={fbxUrl}
        animationName="Walking Animation"
        isOpen={isOpen}
        onClose={() => setIsOpen(false)}
      />
    </>
  );
}
```

## Controls

### Mouse Controls
- **Left Click + Drag**: Rotate camera around the model
- **Right Click + Drag**: Pan the camera
- **Mouse Wheel**: Zoom in/out

### Animation Controls
- **Play/Pause Button**: Start or stop animation playback
- **Restart Button**: Reset animation to beginning
- **Progress Bar**: Click or drag to scrub through animation
- **Time Display**: Shows current time and total duration

### Additional Controls
- **Stats Toggle**: Show/hide performance statistics
- **Fullscreen**: Enter fullscreen mode
- **Volume**: Mute/unmute (for future audio support)

## File Requirements

### FBX Files
- Must be accessible via HTTP/HTTPS
- Should contain embedded animations
- CORS headers must be properly configured
- Recommended file size: < 50MB for web performance

### Supported Features
- Skeletal animations
- Morph target animations
- Multiple animation clips
- Embedded textures
- Material properties

## Dependencies

- `three`: Core 3D library
- `@react-three/fiber`: React renderer for Three.js
- `@react-three/drei`: Useful components and helpers
- `@types/three`: TypeScript definitions

## Performance Considerations

- Large FBX files may take time to load
- Complex animations may impact performance
- Use the Stats component to monitor performance
- Consider model optimization for web use

## Troubleshooting

### Common Issues

1. **CORS Errors**
   - Ensure the FBX file server has proper CORS headers
   - Use a CORS proxy for testing if needed

2. **Loading Failures**
   - Check that the FBX file is valid and accessible
   - Verify the file contains animation data
   - Check browser console for detailed error messages

3. **Performance Issues**
   - Enable Stats to monitor FPS and memory usage
   - Consider reducing model complexity
   - Check for memory leaks in long sessions

### Browser Support
- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

## Future Enhancements

- Audio synchronization support
- Multiple animation clip selection
- Animation blending and transitions
- Export capabilities (screenshots, videos)
- VR/AR preview modes
- Real-time collaboration features
