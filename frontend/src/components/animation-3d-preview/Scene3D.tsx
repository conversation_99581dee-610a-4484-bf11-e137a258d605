import { Suspense } from 'react';
import { OrbitControls, Environment, Grid, Stats } from '@react-three/drei';
import FBXModel from './FBXModel';

interface Scene3DProps {
  fbxUrl: string;
  isPlaying: boolean;
  progress: number;
  showStats?: boolean;
  onLoadComplete: (duration: number) => void;
  onLoadError: (error: string) => void;
}

function LoadingFallback() {
  return (
    <mesh>
      <boxGeometry args={[1, 1, 1]} />
      <meshStandardMaterial color="gray" wireframe />
    </mesh>
  );
}

export default function Scene3D({
  fbxUrl,
  isPlaying,
  progress,
  showStats = false,
  onLoadComplete,
  onLoadError
}: Scene3DProps) {
  return (
    <>
      {/* Camera Controls */}
      <OrbitControls
        enablePan={true}
        enableZoom={true}
        enableRotate={true}
        minDistance={1}
        maxDistance={50}
        target={[0, 1, 0]}
      />

      {/* Lighting */}
      <ambientLight intensity={0.4} />
      <directionalLight
        position={[10, 10, 5]}
        intensity={1}
        castShadow
        shadow-mapSize-width={2048}
        shadow-mapSize-height={2048}
      />
      <directionalLight
        position={[-10, 10, -5]}
        intensity={0.5}
      />

      {/* Environment */}
      <Environment preset="studio" />

      {/* Ground Grid */}
      <Grid
        position={[0, -0.01, 0]}
        args={[10, 10]}
        cellSize={0.5}
        cellThickness={0.5}
        cellColor="#6f6f6f"
        sectionSize={2}
        sectionThickness={1}
        sectionColor="#9d4b4b"
        fadeDistance={25}
        fadeStrength={1}
        followCamera={false}
        infiniteGrid={true}
      />

      {/* FBX Model */}
      <Suspense fallback={<LoadingFallback />}>
        <FBXModel
          url={fbxUrl}
          isPlaying={isPlaying}
          progress={progress}
          onLoadComplete={onLoadComplete}
          onLoadError={onLoadError}
        />
      </Suspense>

      {/* Performance Stats */}
      {showStats && <Stats />}
    </>
  );
}
