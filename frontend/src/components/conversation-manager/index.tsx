'use client';

import { useState, useEffect } from 'react';
import { toast } from 'react-hot-toast';
import { 
  MessageCircle, 
  Plus, 
  Search, 
  Filter, 
  Pin, 
  PinOff, 
  Trash2, 
  Edit3,
  <PERSON>,
  User,
  <PERSON><PERSON>,
  Play
} from 'lucide-react';
import {
  ConversationThread,
  ConversationCreate,
  ConversationUpdate,
  ConversationList,
  ConversationHistory,
  MessageCreate,
  ConversationStatus
} from '@/types/motion';
import {
  getConversations,
  createConversation,
  updateConversation,
  deleteConversation,
  getConversationHistory,
  sendMessage,
  startConversationAnimation
} from '@/lib/api';
import { handleApiError } from '@/lib/api';

interface ConversationManagerProps {
  onSelectConversation?: (conversation: ConversationThread) => void;
}

export default function ConversationManager({ onSelectConversation }: ConversationManagerProps) {
  const [conversations, setConversations] = useState<ConversationThread[]>([]);
  const [selectedConversation, setSelectedConversation] = useState<ConversationThread | null>(null);
  const [conversationHistory, setConversationHistory] = useState<ConversationHistory | null>(null);
  const [loading, setLoading] = useState(false);
  const [creating, setCreating] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<ConversationStatus | ''>('');
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [newMessage, setNewMessage] = useState('');
  const [sendingMessage, setSendingMessage] = useState(false);

  // Form state for creating conversation
  const [newConversation, setNewConversation] = useState<ConversationCreate>({
    title: '',
    description: '',
    character_id: 'default'
  });

  // Load conversations on component mount
  useEffect(() => {
    loadConversations();
  }, [searchQuery, statusFilter]);

  const loadConversations = async () => {
    try {
      setLoading(true);
      const params: any = {};
      if (searchQuery) params.search = searchQuery;
      if (statusFilter) params.status = statusFilter;
      
      const data = await getConversations(params);
      setConversations(data.threads);
    } catch (error) {
      toast.error(handleApiError(error));
    } finally {
      setLoading(false);
    }
  };

  const handleCreateConversation = async () => {
    if (!newConversation.title.trim()) {
      toast.error('Please enter a conversation title');
      return;
    }

    try {
      setCreating(true);
      const conversation = await createConversation(newConversation);
      setConversations(prev => [conversation, ...prev]);
      setNewConversation({ title: '', description: '', character_id: 'default' });
      setShowCreateForm(false);
      toast.success('Conversation created successfully');
    } catch (error) {
      toast.error(handleApiError(error));
    } finally {
      setCreating(false);
    }
  };

  const handleSelectConversation = async (conversation: ConversationThread) => {
    setSelectedConversation(conversation);
    onSelectConversation?.(conversation);
    
    // Load conversation history
    try {
      const history = await getConversationHistory(conversation.id);
      setConversationHistory(history);
    } catch (error) {
      toast.error(handleApiError(error));
    }
  };

  const handleTogglePin = async (conversation: ConversationThread) => {
    try {
      const updated = await updateConversation(conversation.id, {
        is_pinned: !conversation.is_pinned
      });
      setConversations(prev => 
        prev.map(c => c.id === conversation.id ? updated : c)
      );
      toast.success(updated.is_pinned ? 'Conversation pinned' : 'Conversation unpinned');
    } catch (error) {
      toast.error(handleApiError(error));
    }
  };

  const handleDeleteConversation = async (conversation: ConversationThread) => {
    if (!confirm('Are you sure you want to delete this conversation?')) return;

    try {
      await deleteConversation(conversation.id);
      setConversations(prev => prev.filter(c => c.id !== conversation.id));
      if (selectedConversation?.id === conversation.id) {
        setSelectedConversation(null);
        setConversationHistory(null);
      }
      toast.success('Conversation deleted');
    } catch (error) {
      toast.error(handleApiError(error));
    }
  };

  const handleSendMessage = async () => {
    if (!newMessage.trim() || !selectedConversation) return;

    try {
      setSendingMessage(true);
      const message: MessageCreate = {
        content: newMessage,
        message_type: 'user'
      };
      
      await sendMessage(selectedConversation.id, message);
      setNewMessage('');
      
      // Reload conversation history
      const history = await getConversationHistory(selectedConversation.id);
      setConversationHistory(history);
      
      toast.success('Message sent');
    } catch (error) {
      toast.error(handleApiError(error));
    } finally {
      setSendingMessage(false);
    }
  };

  const handleStartAnimation = async (text: string) => {
    if (!selectedConversation) return;

    try {
      const result = await startConversationAnimation(selectedConversation.id, {
        text,
        character_id: selectedConversation.character_id
      });
      toast.success(`Animation generation started (Task ID: ${result.task_id})`);
    } catch (error) {
      toast.error(handleApiError(error));
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const getStatusColor = (status: ConversationStatus) => {
    switch (status) {
      case ConversationStatus.ACTIVE: return 'text-green-600 bg-green-100';
      case ConversationStatus.PAUSED: return 'text-yellow-600 bg-yellow-100';
      case ConversationStatus.COMPLETED: return 'text-blue-600 bg-blue-100';
      case ConversationStatus.ARCHIVED: return 'text-gray-600 bg-gray-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  return (
    <div className="h-full flex">
      {/* Conversation List */}
      <div className="w-1/3 border-r bg-white">
        <div className="p-4 border-b">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold flex items-center gap-2">
              <MessageCircle className="w-5 h-5" />
              Conversations
            </h2>
            <button
              onClick={() => setShowCreateForm(true)}
              className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg"
            >
              <Plus className="w-5 h-5" />
            </button>
          </div>

          {/* Search and Filter */}
          <div className="space-y-2">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Search conversations..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value as ConversationStatus | '')}
              className="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">All Status</option>
              <option value={ConversationStatus.ACTIVE}>Active</option>
              <option value={ConversationStatus.PAUSED}>Paused</option>
              <option value={ConversationStatus.COMPLETED}>Completed</option>
              <option value={ConversationStatus.ARCHIVED}>Archived</option>
            </select>
          </div>
        </div>

        {/* Conversation List */}
        <div className="overflow-y-auto h-full">
          {loading ? (
            <div className="p-4 text-center text-gray-500">Loading conversations...</div>
          ) : conversations.length === 0 ? (
            <div className="p-4 text-center text-gray-500">No conversations found</div>
          ) : (
            conversations.map((conversation) => (
              <div
                key={conversation.id}
                onClick={() => handleSelectConversation(conversation)}
                className={`p-4 border-b cursor-pointer hover:bg-gray-50 ${
                  selectedConversation?.id === conversation.id ? 'bg-blue-50 border-blue-200' : ''
                }`}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      {conversation.is_pinned && <Pin className="w-4 h-4 text-blue-500" />}
                      <h3 className="font-medium truncate">{conversation.title}</h3>
                    </div>
                    {conversation.description && (
                      <p className="text-sm text-gray-600 truncate mb-2">{conversation.description}</p>
                    )}
                    <div className="flex items-center gap-2 text-xs text-gray-500">
                      <span className={`px-2 py-1 rounded-full ${getStatusColor(conversation.status)}`}>
                        {conversation.status}
                      </span>
                      <span>{conversation.message_count} messages</span>
                      <Clock className="w-3 h-3" />
                      <span>{formatDate(conversation.last_activity_at)}</span>
                    </div>
                  </div>
                  <div className="flex items-center gap-1 ml-2">
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleTogglePin(conversation);
                      }}
                      className="p-1 text-gray-400 hover:text-blue-500"
                    >
                      {conversation.is_pinned ? <PinOff className="w-4 h-4" /> : <Pin className="w-4 h-4" />}
                    </button>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDeleteConversation(conversation);
                      }}
                      className="p-1 text-gray-400 hover:text-red-500"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </div>

      {/* Conversation Detail */}
      <div className="flex-1 flex flex-col">
        {selectedConversation ? (
          <>
            {/* Header */}
            <div className="p-4 border-b bg-white">
              <h3 className="font-semibold">{selectedConversation.title}</h3>
              <p className="text-sm text-gray-600">{selectedConversation.description}</p>
            </div>

            {/* Messages */}
            <div className="flex-1 overflow-y-auto p-4 space-y-4">
              {conversationHistory?.messages.map((message) => (
                <div
                  key={message.id}
                  className={`flex ${message.message_type === 'user' ? 'justify-end' : 'justify-start'}`}
                >
                  <div
                    className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                      message.message_type === 'user'
                        ? 'bg-blue-500 text-white'
                        : 'bg-gray-100 text-gray-900'
                    }`}
                  >
                    <div className="flex items-center gap-2 mb-1">
                      {message.message_type === 'user' ? (
                        <User className="w-4 h-4" />
                      ) : (
                        <Bot className="w-4 h-4" />
                      )}
                      <span className="text-xs opacity-75">
                        {formatDate(message.created_at)}
                      </span>
                    </div>
                    <p className="text-sm">{message.content}</p>
                    {message.message_type === 'user' && (
                      <button
                        onClick={() => handleStartAnimation(message.content)}
                        className="mt-2 flex items-center gap-1 text-xs opacity-75 hover:opacity-100"
                      >
                        <Play className="w-3 h-3" />
                        Generate Animation
                      </button>
                    )}
                  </div>
                </div>
              ))}
            </div>

            {/* Message Input */}
            <div className="p-4 border-t bg-white">
              <div className="flex gap-2">
                <input
                  type="text"
                  value={newMessage}
                  onChange={(e) => setNewMessage(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                  placeholder="Type a message..."
                  className="flex-1 px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  disabled={sendingMessage}
                />
                <button
                  onClick={handleSendMessage}
                  disabled={sendingMessage || !newMessage.trim()}
                  className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {sendingMessage ? 'Sending...' : 'Send'}
                </button>
              </div>
            </div>
          </>
        ) : (
          <div className="flex-1 flex items-center justify-center text-gray-500">
            <div className="text-center">
              <MessageCircle className="w-12 h-12 mx-auto mb-4 opacity-50" />
              <p>Select a conversation to view details</p>
            </div>
          </div>
        )}
      </div>

      {/* Create Conversation Modal */}
      {showCreateForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold mb-4">Create New Conversation</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Title *
                </label>
                <input
                  type="text"
                  value={newConversation.title}
                  onChange={(e) => setNewConversation(prev => ({ ...prev, title: e.target.value }))}
                  className="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter conversation title"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Description
                </label>
                <textarea
                  value={newConversation.description}
                  onChange={(e) => setNewConversation(prev => ({ ...prev, description: e.target.value }))}
                  className="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  rows={3}
                  placeholder="Enter conversation description"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Character ID
                </label>
                <input
                  type="text"
                  value={newConversation.character_id}
                  onChange={(e) => setNewConversation(prev => ({ ...prev, character_id: e.target.value }))}
                  className="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="default"
                />
              </div>
            </div>
            <div className="flex gap-2 mt-6">
              <button
                onClick={() => setShowCreateForm(false)}
                className="flex-1 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                onClick={handleCreateConversation}
                disabled={creating || !newConversation.title.trim()}
                className="flex-1 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {creating ? 'Creating...' : 'Create'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
