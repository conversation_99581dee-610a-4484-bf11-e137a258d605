'use client';

import { useState } from 'react';
import { 
  Play, 
  FileText, 
  Clock, 
  Zap,
  Download,
  Monitor
} from 'lucide-react';
import { AnimationResponse } from '@/types/motion';

interface AnimationThumbnailProps {
  response: AnimationResponse;
  onClick?: () => void;
  showControls?: boolean;
  size?: 'small' | 'medium' | 'large';
}

export default function AnimationThumbnail({ 
  response, 
  onClick, 
  showControls = true,
  size = 'medium' 
}: AnimationThumbnailProps) {
  const [isHovered, setIsHovered] = useState(false);

  const sizeClasses = {
    small: 'w-16 h-16',
    medium: 'w-24 h-24',
    large: 'w-32 h-32'
  };

  const iconSizes = {
    small: 'w-4 h-4',
    medium: 'w-5 h-5',
    large: 'w-6 h-6'
  };

  const formatDuration = (seconds: number) => {
    if (seconds < 60) {
      return `${seconds.toFixed(1)}s`;
    }
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toFixed(0).padStart(2, '0')}`;
  };

  return (
    <div 
      className={`relative ${sizeClasses[size]} bg-gradient-to-br from-blue-100 to-purple-100 rounded-lg border-2 border-blue-200 cursor-pointer transition-all duration-200 ${
        isHovered ? 'scale-105 shadow-lg' : 'shadow-md'
      }`}
      onClick={onClick}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-20">
        <svg className="w-full h-full" viewBox="0 0 100 100">
          <defs>
            <pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse">
              <path d="M 10 0 L 0 0 0 10" fill="none" stroke="currentColor" strokeWidth="0.5"/>
            </pattern>
          </defs>
          <rect width="100" height="100" fill="url(#grid)" />
        </svg>
      </div>

      {/* Main Icon */}
      <div className="absolute inset-0 flex items-center justify-center">
        <div className="text-blue-600">
          <FileText className={iconSizes[size]} />
        </div>
      </div>

      {/* Play Button Overlay */}
      {isHovered && (
        <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center rounded-lg">
          <div className="bg-white bg-opacity-90 rounded-full p-2">
            <Play className={`${iconSizes[size]} text-blue-600`} />
          </div>
        </div>
      )}

      {/* Duration Badge */}
      {response.animation_sequence && (
        <div className="absolute top-1 right-1 bg-black bg-opacity-75 text-white text-xs px-1.5 py-0.5 rounded">
          {formatDuration(response.animation_sequence.total_duration)}
        </div>
      )}

      {/* FBX Badge */}
      {response.fbx_file_path && (
        <div className="absolute bottom-1 left-1 bg-green-600 text-white text-xs px-1.5 py-0.5 rounded">
          FBX
        </div>
      )}

      {/* Quick Info Tooltip */}
      {isHovered && size !== 'small' && (
        <div className="absolute -top-2 left-1/2 transform -translate-x-1/2 -translate-y-full bg-gray-900 text-white text-xs px-2 py-1 rounded whitespace-nowrap z-10">
          {response.animation_name || 'Animation'}
          <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-2 border-r-2 border-t-2 border-transparent border-t-gray-900"></div>
        </div>
      )}
    </div>
  );
}
