'use client';

import { useState } from 'react';
import { Play, Download, Eye, Clock, FileVideo } from 'lucide-react';
import Animation3DPreview from '@/components/animation-3d-preview';

interface AnimationThumbnailProps {
  animationUrl: string;
  animationName: string;
  thumbnailUrl?: string;
  duration?: number;
  description?: string;
  size?: 'small' | 'medium' | 'large';
  onPreview?: () => void;
  onDownload?: () => void;
}

export default function AnimationThumbnail({
  animationUrl,
  animationName,
  thumbnailUrl,
  duration,
  description,
  size = 'medium',
  onPreview,
  onDownload
}: AnimationThumbnailProps) {
  const [isPreviewOpen, setIsPreviewOpen] = useState(false);
  const [imageError, setImageError] = useState(false);

  const sizeClasses = {
    small: 'w-24 h-24',
    medium: 'w-32 h-32',
    large: 'w-48 h-48'
  };

  const handlePreview = () => {
    if (onPreview) {
      onPreview();
    } else {
      setIsPreviewOpen(true);
    }
  };

  const handleDownload = () => {
    if (onDownload) {
      onDownload();
    } else {
      // Default download behavior
      const link = document.createElement('a');
      link.href = animationUrl;
      link.download = animationName + '.fbx';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <>
      <div className="group relative bg-white border border-gray-200 rounded-lg overflow-hidden hover:shadow-md transition-shadow">
        {/* Thumbnail */}
        <div className={`${sizeClasses[size]} relative bg-gray-100 flex items-center justify-center overflow-hidden`}>
          {thumbnailUrl && !imageError ? (
            <img
              src={thumbnailUrl}
              alt={animationName}
              className="w-full h-full object-cover"
              onError={() => setImageError(true)}
            />
          ) : (
            <div className="flex flex-col items-center justify-center text-gray-400">
              <FileVideo className="w-8 h-8 mb-1" />
              <span className="text-xs">FBX</span>
            </div>
          )}
          
          {/* Duration Badge */}
          {duration && (
            <div className="absolute bottom-2 right-2 bg-black bg-opacity-75 text-white text-xs px-1.5 py-0.5 rounded flex items-center">
              <Clock className="w-3 h-3 mr-1" />
              {formatDuration(duration)}
            </div>
          )}
          
          {/* Hover Overlay */}
          <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 flex items-center justify-center">
            <button
              onClick={handlePreview}
              className="opacity-0 group-hover:opacity-100 transition-opacity duration-200 bg-white bg-opacity-90 hover:bg-opacity-100 rounded-full p-2"
            >
              <Play className="w-6 h-6 text-gray-700" />
            </button>
          </div>
        </div>
        
        {/* Info */}
        <div className="p-3">
          <h4 className="text-sm font-medium text-gray-900 truncate mb-1">
            {animationName}
          </h4>
          
          {description && (
            <p className="text-xs text-gray-600 line-clamp-2 mb-2">
              {description}
            </p>
          )}
          
          {/* Actions */}
          <div className="flex items-center space-x-1">
            <button
              onClick={handlePreview}
              className="flex-1 inline-flex items-center justify-center px-2 py-1.5 text-xs font-medium text-blue-700 bg-blue-50 rounded hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <Eye className="w-3 h-3 mr-1" />
              Preview
            </button>
            
            <button
              onClick={handleDownload}
              className="flex-1 inline-flex items-center justify-center px-2 py-1.5 text-xs font-medium text-gray-700 bg-gray-50 rounded hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-500"
            >
              <Download className="w-3 h-3 mr-1" />
              Download
            </button>
          </div>
        </div>
      </div>

      <Animation3DPreview
        fbxUrl={animationUrl}
        animationName={animationName}
        isOpen={isPreviewOpen}
        onClose={() => setIsPreviewOpen(false)}
      />
    </>
  );
}
