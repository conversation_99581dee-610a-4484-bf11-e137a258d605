'use client';

import { useState } from 'react';
import { Play, Download, Eye } from 'lucide-react';
import Animation3DPreview from '@/components/animation-3d-preview';

interface ChatAnimationPreviewProps {
  animationUrl: string;
  animationName: string;
  description?: string;
  onDownload?: () => void;
}

export default function ChatAnimationPreview({
  animationUrl,
  animationName,
  description,
  onDownload
}: ChatAnimationPreviewProps) {
  const [isPreviewOpen, setIsPreviewOpen] = useState(false);

  const handlePreview = () => {
    setIsPreviewOpen(true);
  };

  const handleDownload = () => {
    if (onDownload) {
      onDownload();
    } else {
      // Default download behavior
      const link = document.createElement('a');
      link.href = animationUrl;
      link.download = animationName + '.fbx';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  return (
    <>
      <div className="bg-gradient-to-br from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-4 max-w-md">
        <div className="flex items-start space-x-3">
          <div className="flex-shrink-0">
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <Play className="w-6 h-6 text-blue-600" />
            </div>
          </div>
          
          <div className="flex-1 min-w-0">
            <h4 className="text-sm font-medium text-gray-900 truncate">
              {animationName}
            </h4>
            {description && (
              <p className="text-xs text-gray-600 mt-1 line-clamp-2">
                {description}
              </p>
            )}
            
            <div className="flex items-center space-x-2 mt-3">
              <button
                onClick={handlePreview}
                className="inline-flex items-center px-3 py-1.5 text-xs font-medium text-blue-700 bg-blue-100 rounded-md hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <Eye className="w-3 h-3 mr-1" />
                Preview
              </button>
              
              <button
                onClick={handleDownload}
                className="inline-flex items-center px-3 py-1.5 text-xs font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500"
              >
                <Download className="w-3 h-3 mr-1" />
                Download
              </button>
            </div>
          </div>
        </div>
      </div>

      <Animation3DPreview
        fbxUrl={animationUrl}
        animationName={animationName}
        isOpen={isPreviewOpen}
        onClose={() => setIsPreviewOpen(false)}
      />
    </>
  );
}
