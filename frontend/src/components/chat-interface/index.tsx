'use client';

import { useState, useEffect, useRef } from 'react';
import { toast } from 'react-hot-toast';
import { 
  Send, 
  Bot, 
  User, 
  Loader2,
  Play,
  Download,
  Clock
} from 'lucide-react';
import {
  ConversationThread,
  ConversationHistory,
  MessageCreate,
  Message,
  AnimationResponse
} from '@/types/motion';
import {
  getConversationHistory,
  sendMessage,
  startConversationAnimation,
  generateProfessionalAnimation
} from '@/lib/api';
import { handleApiError } from '@/lib/api';
import ChatAnimationPreview from './ChatAnimationPreview';
import AnimationThumbnail from './AnimationThumbnail';

interface ChatInterfaceProps {
  conversation: ConversationThread | null;
  onConversationUpdate?: (conversation: ConversationThread) => void;
}

export default function ChatInterface({ conversation, onConversationUpdate }: ChatInterfaceProps) {
  const [messages, setMessages] = useState<Message[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [loading, setLoading] = useState(false);
  const [sendingMessage, setSendingMessage] = useState(false);
  const [generatingAnimation, setGeneratingAnimation] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Load conversation history when conversation changes
  useEffect(() => {
    if (conversation) {
      loadConversationHistory();
    } else {
      setMessages([]);
    }
  }, [conversation]);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const loadConversationHistory = async () => {
    if (!conversation) return;

    try {
      setLoading(true);
      const history = await getConversationHistory(conversation.id);
      setMessages(history.messages);
    } catch (error) {
      toast.error(handleApiError(error));
    } finally {
      setLoading(false);
    }
  };

  const handleSendMessage = async () => {
    if (!newMessage.trim() || !conversation || sendingMessage) return;

    const messageContent = newMessage.trim();
    setNewMessage('');

    try {
      setSendingMessage(true);
      
      // Add user message to UI immediately
      const userMessage: Message = {
        id: `temp-${Date.now()}`,
        conversation_id: conversation.id,
        content: messageContent,
        message_type: 'user',
        metadata: {},
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };
      setMessages(prev => [...prev, userMessage]);

      // Send message to backend
      const messageData: MessageCreate = {
        content: messageContent,
        message_type: 'user'
      };
      
      await sendMessage(conversation.id, messageData);
      
      // Reload conversation history to get the actual message and any AI response
      await loadConversationHistory();
      
    } catch (error) {
      toast.error(handleApiError(error));
      // Remove the temporary message on error
      setMessages(prev => prev.filter(m => m.id !== `temp-${Date.now()}`));
    } finally {
      setSendingMessage(false);
    }
  };

  const handleGenerateAnimation = async (messageContent: string, messageId?: string) => {
    if (!conversation) return;

    try {
      setGeneratingAnimation(true);

      // Use the animation generation API
      const response = await generateProfessionalAnimation({
        text: messageContent,
        character_id: conversation.character_id || 'default',
        style: 'professional',
        complexity: 'medium'
      });

      toast.success('Animation generated successfully!');

      // Update the existing message with animation response or create new system message
      if (messageId) {
        setMessages(prev => prev.map(msg =>
          msg.id === messageId
            ? {
                ...msg,
                metadata: {
                  ...msg.metadata,
                  animation_response: response,
                  type: 'animation_generated'
                }
              }
            : msg
        ));
      } else {
        // Add system message about animation generation
        const systemMessage: Message = {
          id: `anim-${Date.now()}`,
          conversation_id: conversation.id,
          content: `Animation generated: ${response.animation_name || 'Untitled Animation'}`,
          message_type: 'system',
          metadata: {
            animation_response: response,
            type: 'animation_generated'
          },
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };

        setMessages(prev => [...prev, systemMessage]);
      }

    } catch (error) {
      toast.error(handleApiError(error));
    } finally {
      setGeneratingAnimation(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const formatMessageTime = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  if (!conversation) {
    return (
      <div className="flex-1 flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <Bot className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-gray-600 mb-2">
            Welcome to Motion Agent
          </h3>
          <p className="text-gray-500">
            Start a new conversation to generate 3D animations from natural language
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 flex flex-col bg-white">
      {/* Chat Header */}
      <div className="border-b bg-white px-6 py-4">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-lg font-semibold text-gray-900">
              {conversation.title}
            </h2>
            <p className="text-sm text-gray-500">
              {conversation.message_count} messages • Last active {formatMessageTime(conversation.last_activity_at)}
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
              {conversation.status}
            </span>
          </div>
        </div>
      </div>

      {/* Messages Area */}
      <div className="flex-1 overflow-y-auto px-6 py-4 space-y-4">
        {loading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="w-6 h-6 animate-spin text-blue-500" />
            <span className="ml-2 text-gray-600">Loading conversation...</span>
          </div>
        ) : messages.length === 0 ? (
          <div className="text-center py-8">
            <Bot className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500">
              No messages yet. Start the conversation by describing an animation you'd like to create.
            </p>
          </div>
        ) : (
          messages.map((message) => (
            <div
              key={message.id}
              className={`flex ${message.message_type === 'user' ? 'justify-end' : 'justify-start'}`}
            >
              <div
                className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                  message.message_type === 'user'
                    ? 'bg-blue-500 text-white'
                    : message.message_type === 'system'
                    ? 'bg-gray-100 text-gray-800 border'
                    : 'bg-gray-100 text-gray-800'
                }`}
              >
                <div className="flex items-start space-x-2">
                  {message.message_type === 'user' ? (
                    <User className="w-4 h-4 mt-0.5 flex-shrink-0" />
                  ) : message.message_type === 'system' ? (
                    <Bot className="w-4 h-4 mt-0.5 flex-shrink-0 text-blue-500" />
                  ) : (
                    <Bot className="w-4 h-4 mt-0.5 flex-shrink-0" />
                  )}
                  <div className="flex-1">
                    <div className="flex items-start space-x-3">
                      <div className="flex-1">
                        <p className="text-sm">{message.content}</p>

                        {/* Animation generation result */}
                        {message.metadata?.animation_response && (
                          <ChatAnimationPreview
                            response={message.metadata.animation_response as AnimationResponse}
                            onRegenerate={() => handleGenerateAnimation(message.content, message.id)}
                          />
                        )}
                      </div>

                      {/* Animation thumbnail for system messages */}
                      {message.message_type === 'system' && message.metadata?.animation_response && (
                        <div className="flex-shrink-0">
                          <AnimationThumbnail
                            response={message.metadata.animation_response as AnimationResponse}
                            size="small"
                            showControls={false}
                          />
                        </div>
                      )}
                    </div>
                    
                    {/* Generate animation button for user messages */}
                    {message.message_type === 'user' && !message.metadata?.animation_response && (
                      <button
                        onClick={() => handleGenerateAnimation(message.content, message.id)}
                        disabled={generatingAnimation}
                        className="mt-2 inline-flex items-center px-2 py-1 text-xs bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
                      >
                        {generatingAnimation ? (
                          <Loader2 className="w-3 h-3 animate-spin mr-1" />
                        ) : (
                          <Play className="w-3 h-3 mr-1" />
                        )}
                        Generate Animation
                      </button>
                    )}
                    
                    <div className="flex items-center justify-between mt-1">
                      <span className="text-xs opacity-75">
                        {formatMessageTime(message.created_at)}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))
        )}
        <div ref={messagesEndRef} />
      </div>

      {/* Message Input */}
      <div className="border-t bg-white px-6 py-4">
        <div className="flex items-end space-x-4">
          <div className="flex-1">
            <textarea
              value={newMessage}
              onChange={(e) => setNewMessage(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Describe the animation you want to create..."
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
              rows={3}
              disabled={sendingMessage}
            />
          </div>
          <button
            onClick={handleSendMessage}
            disabled={!newMessage.trim() || sendingMessage}
            className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
          >
            {sendingMessage ? (
              <Loader2 className="w-4 h-4 animate-spin" />
            ) : (
              <Send className="w-4 h-4" />
            )}
          </button>
        </div>
      </div>
    </div>
  );
}
