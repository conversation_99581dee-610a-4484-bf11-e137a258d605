'use client';

import { useState, useEffect } from 'react';
import { toast } from 'react-hot-toast';
import { 
  Activity, 
  Clock, 
  CheckCircle, 
  XCircle, 
  AlertCircle, 
  Pause, 
  Play, 
  RotateCcw,
  Search,
  BarChart3,
  TrendingUp,
} from 'lucide-react';
import {
  Task,
  TaskList,
  TaskStats,
  TaskType,
  TaskStatus,
  TaskPriority
} from '@/types/motion';
import {
  getTasks,
  getTask,
  cancelTask,
  getTaskStats
} from '@/lib/api';
import { handleApiError } from '@/lib/api';

interface TaskMonitorProps {
  conversationId?: string;
}

export default function TaskMonitor({ conversationId }: TaskMonitorProps) {
  const [tasks, setTasks] = useState<Task[]>([]);
  const [selectedTask, setSelectedTask] = useState<Task | null>(null);
  const [taskStats, setTaskStats] = useState<TaskStats | null>(null);
  const [loading, setLoading] = useState(false);
  const [statsLoading, setStatsLoading] = useState(false);
  
  // Filters
  const [typeFilter, setTypeFilter] = useState<TaskType | ''>('');
  const [statusFilter, setStatusFilter] = useState<TaskStatus | ''>('');
  const [priorityFilter, setPriorityFilter] = useState<TaskPriority | ''>('');
  const [searchQuery, setSearchQuery] = useState('');

  // Auto-refresh
  const [autoRefresh, setAutoRefresh] = useState(true);

  useEffect(() => {
    loadTasks();
    loadTaskStats();
  }, [typeFilter, statusFilter, priorityFilter, searchQuery, conversationId]);

  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(() => {
      loadTasks();
      loadTaskStats();
    }, 5000); // Refresh every 5 seconds

    return () => clearInterval(interval);
  }, [autoRefresh, typeFilter, statusFilter, priorityFilter, searchQuery, conversationId]);

  const loadTasks = async () => {
    try {
      setLoading(true);
      const params: any = {};
      if (typeFilter) params.task_type = typeFilter;
      if (statusFilter) params.status = statusFilter;
      if (priorityFilter) params.priority = priorityFilter;
      if (searchQuery) params.search = searchQuery;
      if (conversationId) params.conversation_id = conversationId;

      const data = await getTasks(params);
      setTasks(data.tasks);
    } catch (error) {
      toast.error(handleApiError(error));
    } finally {
      setLoading(false);
    }
  };

  const loadTaskStats = async () => {
    try {
      setStatsLoading(true);
      const stats = await getTaskStats();
      setTaskStats(stats);
    } catch (error) {
      console.error('Failed to load task stats:', error);
    } finally {
      setStatsLoading(false);
    }
  };

  const handleSelectTask = async (task: Task) => {
    try {
      const detailedTask = await getTask(task.id);
      setSelectedTask(detailedTask);
    } catch (error) {
      toast.error(handleApiError(error));
    }
  };

  const handleCancelTask = async (task: Task) => {
    if (!confirm('Are you sure you want to cancel this task?')) return;

    try {
      await cancelTask(task.id);
      await loadTasks();
      toast.success('Task cancelled');
    } catch (error) {
      toast.error(handleApiError(error));
    }
  };

  const getStatusIcon = (status: TaskStatus) => {
    switch (status) {
      case TaskStatus.PENDING: return <Clock className="w-4 h-4 text-gray-500" />;
      case TaskStatus.QUEUED: return <Pause className="w-4 h-4 text-yellow-500" />;
      case TaskStatus.RUNNING: return <Play className="w-4 h-4 text-blue-500" />;
      case TaskStatus.COMPLETED: return <CheckCircle className="w-4 h-4 text-green-500" />;
      case TaskStatus.FAILED: return <XCircle className="w-4 h-4 text-red-500" />;
      case TaskStatus.CANCELLED: return <XCircle className="w-4 h-4 text-gray-500" />;
      case TaskStatus.RETRYING: return <RotateCcw className="w-4 h-4 text-orange-500" />;
      default: return <AlertCircle className="w-4 h-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: TaskStatus) => {
    switch (status) {
      case TaskStatus.PENDING: return 'text-gray-600 bg-gray-100';
      case TaskStatus.QUEUED: return 'text-yellow-600 bg-yellow-100';
      case TaskStatus.RUNNING: return 'text-blue-600 bg-blue-100';
      case TaskStatus.COMPLETED: return 'text-green-600 bg-green-100';
      case TaskStatus.FAILED: return 'text-red-600 bg-red-100';
      case TaskStatus.CANCELLED: return 'text-gray-600 bg-gray-100';
      case TaskStatus.RETRYING: return 'text-orange-600 bg-orange-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getPriorityColor = (priority: TaskPriority) => {
    switch (priority) {
      case TaskPriority.LOW: return 'text-gray-600 bg-gray-100';
      case TaskPriority.NORMAL: return 'text-blue-600 bg-blue-100';
      case TaskPriority.HIGH: return 'text-orange-600 bg-orange-100';
      case TaskPriority.URGENT: return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const formatDuration = (seconds?: number) => {
    if (!seconds) return 'N/A';
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}m ${secs}s`;
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleString();
  };

  return (
    <div className="h-full flex flex-col">
      {/* Header with Stats */}
      <div className="p-6 bg-white border-b">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold flex items-center gap-2">
            <Activity className="w-6 h-6" />
            Task Monitor
            {conversationId && <span className="text-sm text-gray-500">(Conversation Tasks)</span>}
          </h2>
          <div className="flex items-center gap-2">
            <label className="flex items-center gap-2 text-sm">
              <input
                type="checkbox"
                checked={autoRefresh}
                onChange={(e) => setAutoRefresh(e.target.checked)}
                className="rounded"
              />
              Auto-refresh
            </label>
          </div>
        </div>

        {/* Stats Cards */}
        {taskStats && !statsLoading && (
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4 mb-6">
            <div className="bg-blue-50 p-4 rounded-lg">
              <div className="flex items-center gap-2 mb-1">
                <BarChart3 className="w-4 h-4 text-blue-600" />
                <span className="text-sm text-blue-600">Total</span>
              </div>
              <div className="text-2xl font-bold text-blue-700">{taskStats.total_tasks}</div>
            </div>
            <div className="bg-yellow-50 p-4 rounded-lg">
              <div className="flex items-center gap-2 mb-1">
                <Clock className="w-4 h-4 text-yellow-600" />
                <span className="text-sm text-yellow-600">Pending</span>
              </div>
              <div className="text-2xl font-bold text-yellow-700">{taskStats.pending_tasks}</div>
            </div>
            <div className="bg-blue-50 p-4 rounded-lg">
              <div className="flex items-center gap-2 mb-1">
                <Play className="w-4 h-4 text-blue-600" />
                <span className="text-sm text-blue-600">Running</span>
              </div>
              <div className="text-2xl font-bold text-blue-700">{taskStats.running_tasks}</div>
            </div>
            <div className="bg-green-50 p-4 rounded-lg">
              <div className="flex items-center gap-2 mb-1">
                <CheckCircle className="w-4 h-4 text-green-600" />
                <span className="text-sm text-green-600">Completed</span>
              </div>
              <div className="text-2xl font-bold text-green-700">{taskStats.completed_tasks}</div>
            </div>
            <div className="bg-red-50 p-4 rounded-lg">
              <div className="flex items-center gap-2 mb-1">
                <XCircle className="w-4 h-4 text-red-600" />
                <span className="text-sm text-red-600">Failed</span>
              </div>
              <div className="text-2xl font-bold text-red-700">{taskStats.failed_tasks}</div>
            </div>
            <div className="bg-purple-50 p-4 rounded-lg">
              <div className="flex items-center gap-2 mb-1">
                <TrendingUp className="w-4 h-4 text-purple-600" />
                <span className="text-sm text-purple-600">Success Rate</span>
              </div>
              <div className="text-2xl font-bold text-purple-700">{taskStats.success_rate.toFixed(1)}%</div>
            </div>
          </div>
        )}

        {/* Filters */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Search tasks..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          <select
            value={typeFilter}
            onChange={(e) => setTypeFilter(e.target.value as TaskType | '')}
            className="px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="">All Types</option>
            <option value={TaskType.ANIMATION_GENERATION}>Animation Generation</option>
            <option value={TaskType.NLU_PROCESSING}>NLU Processing</option>
            <option value={TaskType.MOTION_CAPTURE}>Motion Capture</option>
            <option value={TaskType.BLENDER_EXPORT}>Blender Export</option>
            <option value={TaskType.CONVERSATION_PROCESSING}>Conversation Processing</option>
            <option value={TaskType.SYSTEM_MAINTENANCE}>System Maintenance</option>
          </select>
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value as TaskStatus | '')}
            className="px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="">All Status</option>
            <option value={TaskStatus.PENDING}>Pending</option>
            <option value={TaskStatus.QUEUED}>Queued</option>
            <option value={TaskStatus.RUNNING}>Running</option>
            <option value={TaskStatus.COMPLETED}>Completed</option>
            <option value={TaskStatus.FAILED}>Failed</option>
            <option value={TaskStatus.CANCELLED}>Cancelled</option>
            <option value={TaskStatus.RETRYING}>Retrying</option>
          </select>
          <select
            value={priorityFilter}
            onChange={(e) => setPriorityFilter(e.target.value as TaskPriority | '')}
            className="px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="">All Priorities</option>
            <option value={TaskPriority.LOW}>Low</option>
            <option value={TaskPriority.NORMAL}>Normal</option>
            <option value={TaskPriority.HIGH}>High</option>
            <option value={TaskPriority.URGENT}>Urgent</option>
          </select>
        </div>
      </div>

      {/* Task List and Detail */}
      <div className="flex-1 flex">
        {/* Task List */}
        <div className="w-1/2 border-r overflow-y-auto">
          {loading ? (
            <div className="p-4 text-center text-gray-500">Loading tasks...</div>
          ) : tasks.length === 0 ? (
            <div className="p-4 text-center text-gray-500">No tasks found</div>
          ) : (
            <div className="divide-y">
              {tasks.map((task) => (
                <div
                  key={task.id}
                  onClick={() => handleSelectTask(task)}
                  className={`p-4 cursor-pointer hover:bg-gray-50 ${
                    selectedTask?.id === task.id ? 'bg-blue-50 border-blue-200' : ''
                  }`}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-2">
                        {getStatusIcon(task.status)}
                        <h3 className="font-medium truncate">{task.task_name}</h3>
                        <span className={`px-2 py-1 text-xs rounded-full ${getPriorityColor(task.priority)}`}>
                          {task.priority}
                        </span>
                      </div>
                      <div className="flex items-center gap-4 text-sm text-gray-600 mb-2">
                        <span className={`px-2 py-1 rounded-full ${getStatusColor(task.status)}`}>
                          {task.status}
                        </span>
                        <span>{task.task_type.replace('_', ' ')}</span>
                      </div>
                      {task.status === TaskStatus.RUNNING && (
                        <div className="mb-2">
                          <div className="flex justify-between text-sm text-gray-600 mb-1">
                            <span>Progress</span>
                            <span>{task.progress.toFixed(1)}%</span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div
                              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                              style={{ width: `${task.progress}%` }}
                            />
                          </div>
                        </div>
                      )}
                      <div className="text-xs text-gray-500">
                        Created: {formatDate(task.created_at)}
                        {task.actual_duration && (
                          <span className="ml-4">Duration: {formatDuration(task.actual_duration)}</span>
                        )}
                      </div>
                    </div>
                    {(task.status === TaskStatus.PENDING || task.status === TaskStatus.QUEUED || task.status === TaskStatus.RUNNING) && (
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleCancelTask(task);
                        }}
                        className="ml-2 p-1 text-gray-400 hover:text-red-500"
                      >
                        <XCircle className="w-4 h-4" />
                      </button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Task Detail */}
        <div className="w-1/2 p-6 overflow-y-auto">
          {selectedTask ? (
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold mb-2">{selectedTask.task_name}</h3>
                <p className="text-gray-600">{selectedTask.description}</p>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
                  <div className="flex items-center gap-2">
                    {getStatusIcon(selectedTask.status)}
                    <span className={`px-2 py-1 rounded-full ${getStatusColor(selectedTask.status)}`}>
                      {selectedTask.status}
                    </span>
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Priority</label>
                  <span className={`px-2 py-1 rounded-full ${getPriorityColor(selectedTask.priority)}`}>
                    {selectedTask.priority}
                  </span>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Type</label>
                  <span className="text-gray-900">{selectedTask.task_type.replace('_', ' ')}</span>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Progress</label>
                  <span className="text-gray-900">{selectedTask.progress.toFixed(1)}%</span>
                </div>
              </div>

              {selectedTask.status === TaskStatus.RUNNING && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Progress</label>
                  <div className="w-full bg-gray-200 rounded-full h-3">
                    <div
                      className="bg-blue-600 h-3 rounded-full transition-all duration-300"
                      style={{ width: `${selectedTask.progress}%` }}
                    />
                  </div>
                </div>
              )}

              <div className="grid grid-cols-1 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Created</label>
                  <span className="text-gray-900">{formatDate(selectedTask.created_at)}</span>
                </div>
                {selectedTask.started_at && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Started</label>
                    <span className="text-gray-900">{formatDate(selectedTask.started_at)}</span>
                  </div>
                )}
                {selectedTask.completed_at && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Completed</label>
                    <span className="text-gray-900">{formatDate(selectedTask.completed_at)}</span>
                  </div>
                )}
                {selectedTask.actual_duration && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Duration</label>
                    <span className="text-gray-900">{formatDuration(selectedTask.actual_duration)}</span>
                  </div>
                )}
              </div>

              {selectedTask.error_message && (
                <div>
                  <label className="block text-sm font-medium text-red-700 mb-1">Error</label>
                  <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                    <p className="text-red-700 text-sm">{selectedTask.error_message}</p>
                    {selectedTask.error_code && (
                      <p className="text-red-600 text-xs mt-1">Code: {selectedTask.error_code}</p>
                    )}
                  </div>
                </div>
              )}

              {Object.keys(selectedTask.input_data).length > 0 && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Input Data</label>
                  <pre className="p-3 bg-gray-50 border rounded-lg text-xs overflow-x-auto">
                    {JSON.stringify(selectedTask.input_data, null, 2)}
                  </pre>
                </div>
              )}

              {Object.keys(selectedTask.output_data).length > 0 && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Output Data</label>
                  <pre className="p-3 bg-gray-50 border rounded-lg text-xs overflow-x-auto">
                    {JSON.stringify(selectedTask.output_data, null, 2)}
                  </pre>
                </div>
              )}
            </div>
          ) : (
            <div className="flex items-center justify-center h-full text-gray-500">
              <div className="text-center">
                <Activity className="w-12 h-12 mx-auto mb-4 opacity-50" />
                <p>Select a task to view details</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
