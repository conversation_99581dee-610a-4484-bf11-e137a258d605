'use client';

import { useState } from 'react';
import { toast } from 'react-hot-toast';
import { 
  <PERSON>, 
  Z<PERSON>, 
  <PERSON>ting<PERSON>, 
  User, 
  <PERSON>,
  Spark<PERSON>,
  Download,
  RefreshCw
} from 'lucide-react';
import {
  MotionRequest,
  AdvancedMotionRequest,
  MotionResponse
} from '@/types/motion';
import {
  generateMotion,
  generateAdvancedMotion
} from '@/lib/api';
import { handleApiError } from '@/lib/api';

interface MotionGeneratorAdvancedProps {
  onResponse?: (response: MotionResponse) => void;
  onLoading?: (loading: boolean) => void;
  onError?: (error: string | null) => void;
}

export default function MotionGeneratorAdvanced({ 
  onResponse, 
  onLoading, 
  onError 
}: MotionGeneratorAdvancedProps) {
  const [text, setText] = useState('');
  const [characterId, setCharacterId] = useState('default');
  const [useAdvanced, setUseAdvanced] = useState(true);
  const [useLangGraph, setUseLangGraph] = useState(true);
  const [context, setContext] = useState<Record<string, any>>({});
  const [loading, setLoading] = useState(false);
  const [response, setResponse] = useState<MotionResponse | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [showAdvancedSettings, setShowAdvancedSettings] = useState(false);

  // Advanced settings
  const [advancedSettings, setAdvancedSettings] = useState({
    complexity: 'medium',
    style: 'realistic',
    duration: 5,
    smoothing: true,
    physics: true
  });

  const handleGenerate = async () => {
    if (!text.trim()) {
      toast.error('Please enter a motion description');
      return;
    }

    try {
      setLoading(true);
      setError(null);
      onLoading?.(true);
      onError?.(null);

      let result: MotionResponse;

      if (useAdvanced) {
        const request: AdvancedMotionRequest = {
          text,
          character_id: characterId,
          use_langgraph: useLangGraph,
          context: {
            ...context,
            ...advancedSettings
          }
        };
        result = await generateAdvancedMotion(request);
      } else {
        const request: MotionRequest = {
          text,
          character_id: characterId,
          context: advancedSettings
        };
        result = await generateMotion(request);
      }

      setResponse(result);
      onResponse?.(result);

      if (result.success) {
        toast.success('Motion generated successfully!');
      } else {
        toast.error(result.error_message || 'Motion generation failed');
      }
    } catch (err) {
      const errorMessage = handleApiError(err);
      setError(errorMessage);
      onError?.(errorMessage);
      toast.error(errorMessage);
    } finally {
      setLoading(false);
      onLoading?.(false);
    }
  };

  const handleClear = () => {
    setText('');
    setResponse(null);
    setError(null);
    onResponse?.(null);
    onError?.(null);
  };

  const examplePrompts = [
    "A character walking forward confidently",
    "Jumping over a small obstacle",
    "Waving hello with both hands",
    "Performing a backflip",
    "Dancing to upbeat music",
    "Sitting down and standing up",
    "Running and then stopping suddenly",
    "Throwing a ball with precision"
  ];

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-semibold flex items-center gap-2">
          {useAdvanced ? (
            <>
              <Zap className="w-6 h-6 text-purple-600" />
              Advanced Motion Generator
            </>
          ) : (
            <>
              <Play className="w-6 h-6 text-blue-600" />
              Basic Motion Generator
            </>
          )}
        </h2>
        <div className="flex items-center gap-2">
          <button
            onClick={() => setShowAdvancedSettings(!showAdvancedSettings)}
            className={`p-2 rounded-lg ${showAdvancedSettings ? 'bg-blue-100 text-blue-600' : 'bg-gray-100 text-gray-600'}`}
          >
            <Settings className="w-5 h-5" />
          </button>
        </div>
      </div>

      {/* Mode Toggle */}
      <div className="mb-6">
        <div className="flex items-center gap-4 p-3 bg-gray-50 rounded-lg">
          <label className="flex items-center gap-2">
            <input
              type="radio"
              name="mode"
              checked={!useAdvanced}
              onChange={() => setUseAdvanced(false)}
              className="text-blue-600"
            />
            <User className="w-4 h-4" />
            <span>Basic Mode</span>
          </label>
          <label className="flex items-center gap-2">
            <input
              type="radio"
              name="mode"
              checked={useAdvanced}
              onChange={() => setUseAdvanced(true)}
              className="text-purple-600"
            />
            <Brain className="w-4 h-4" />
            <span>Advanced Mode</span>
          </label>
          {useAdvanced && (
            <label className="flex items-center gap-2 ml-4">
              <input
                type="checkbox"
                checked={useLangGraph}
                onChange={(e) => setUseLangGraph(e.target.checked)}
                className="text-purple-600"
              />
              <Sparkles className="w-4 h-4" />
              <span>Use LangGraph</span>
            </label>
          )}
        </div>
      </div>

      {/* Advanced Settings */}
      {showAdvancedSettings && (
        <div className="mb-6 p-4 bg-gray-50 rounded-lg">
          <h3 className="font-medium mb-4">Advanced Settings</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Character ID
              </label>
              <input
                type="text"
                value={characterId}
                onChange={(e) => setCharacterId(e.target.value)}
                className="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="default"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Complexity
              </label>
              <select
                value={advancedSettings.complexity}
                onChange={(e) => setAdvancedSettings(prev => ({ ...prev, complexity: e.target.value }))}
                className="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="simple">Simple</option>
                <option value="medium">Medium</option>
                <option value="complex">Complex</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Animation Style
              </label>
              <select
                value={advancedSettings.style}
                onChange={(e) => setAdvancedSettings(prev => ({ ...prev, style: e.target.value }))}
                className="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="realistic">Realistic</option>
                <option value="cartoon">Cartoon</option>
                <option value="stylized">Stylized</option>
                <option value="mechanical">Mechanical</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Duration (seconds)
              </label>
              <input
                type="number"
                min="1"
                max="30"
                value={advancedSettings.duration}
                onChange={(e) => setAdvancedSettings(prev => ({ ...prev, duration: parseInt(e.target.value) }))}
                className="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            <div className="flex items-center gap-4">
              <label className="flex items-center gap-2">
                <input
                  type="checkbox"
                  checked={advancedSettings.smoothing}
                  onChange={(e) => setAdvancedSettings(prev => ({ ...prev, smoothing: e.target.checked }))}
                  className="text-blue-600"
                />
                <span className="text-sm">Motion Smoothing</span>
              </label>
              <label className="flex items-center gap-2">
                <input
                  type="checkbox"
                  checked={advancedSettings.physics}
                  onChange={(e) => setAdvancedSettings(prev => ({ ...prev, physics: e.target.checked }))}
                  className="text-blue-600"
                />
                <span className="text-sm">Physics Simulation</span>
              </label>
            </div>
          </div>
        </div>
      )}

      {/* Text Input */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Motion Description
        </label>
        <textarea
          value={text}
          onChange={(e) => setText(e.target.value)}
          placeholder="Describe the motion you want to generate..."
          className="w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
          rows={4}
          disabled={loading}
        />
      </div>

      {/* Example Prompts */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Example Prompts
        </label>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
          {examplePrompts.map((prompt, index) => (
            <button
              key={index}
              onClick={() => setText(prompt)}
              className="text-left p-2 text-sm bg-gray-50 hover:bg-gray-100 rounded border text-gray-700"
              disabled={loading}
            >
              {prompt}
            </button>
          ))}
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex gap-3 mb-6">
        <button
          onClick={handleGenerate}
          disabled={loading || !text.trim()}
          className={`flex-1 flex items-center justify-center gap-2 px-4 py-3 rounded-lg font-medium transition-colors ${
            useAdvanced
              ? 'bg-purple-600 hover:bg-purple-700 text-white'
              : 'bg-blue-600 hover:bg-blue-700 text-white'
          } disabled:opacity-50 disabled:cursor-not-allowed`}
        >
          {loading ? (
            <>
              <RefreshCw className="w-5 h-5 animate-spin" />
              Generating...
            </>
          ) : useAdvanced ? (
            <>
              <Zap className="w-5 h-5" />
              Generate Advanced Motion
            </>
          ) : (
            <>
              <Play className="w-5 h-5" />
              Generate Motion
            </>
          )}
        </button>
        <button
          onClick={handleClear}
          disabled={loading}
          className="px-4 py-3 border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Clear
        </button>
      </div>

      {/* Response Display */}
      {response && (
        <div className="border rounded-lg p-4">
          <h3 className="font-medium mb-3 flex items-center gap-2">
            {response.success ? (
              <>
                <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                Motion Generated Successfully
              </>
            ) : (
              <>
                <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                Motion Generation Failed
              </>
            )}
          </h3>

          <div className="space-y-3">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Message
              </label>
              <p className="text-gray-900">{response.message}</p>
            </div>

            {response.action_sequence && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Action Sequence
                </label>
                <div className="bg-gray-50 p-3 rounded border">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                    <div>
                      <span className="font-medium">Actions:</span>
                      <div className="mt-1">
                        {response.action_sequence.actions?.map((action, index) => (
                          <span key={index} className="inline-block bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs mr-1 mb-1">
                            {action}
                          </span>
                        ))}
                      </div>
                    </div>
                    <div>
                      <span className="font-medium">Duration:</span>
                      <div className="mt-1">{response.action_sequence.duration}s</div>
                    </div>
                    {response.action_sequence.complexity && (
                      <div>
                        <span className="font-medium">Complexity:</span>
                        <div className="mt-1 capitalize">{response.action_sequence.complexity}</div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}

            {response.error_message && (
              <div>
                <label className="block text-sm font-medium text-red-700 mb-1">
                  Error
                </label>
                <div className="bg-red-50 border border-red-200 p-3 rounded">
                  <p className="text-red-700 text-sm">{response.error_message}</p>
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Error Display */}
      {error && (
        <div className="border border-red-200 bg-red-50 rounded-lg p-4">
          <h3 className="font-medium text-red-800 mb-2">Error</h3>
          <p className="text-red-700 text-sm">{error}</p>
        </div>
      )}
    </div>
  );
}
