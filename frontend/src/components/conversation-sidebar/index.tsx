'use client';

import { useState, useEffect } from 'react';
import { toast } from 'react-hot-toast';
import { 
  Plus, 
  MessageCircle, 
  Search, 
  Pin, 
  PinOff, 
  Trash2, 
  Edit3,
  Clock,
  MoreVertical,
  X
} from 'lucide-react';
import {
  ConversationThread,
  ConversationCreate,
  ConversationList,
  ConversationStatus
} from '@/types/motion';
import {
  getConversations,
  createConversation,
  updateConversation,
  deleteConversation
} from '@/lib/api';
import { handleApiError } from '@/lib/api';

interface ConversationSidebarProps {
  selectedConversation: ConversationThread | null;
  onSelectConversation: (conversation: ConversationThread) => void;
  onNewConversation: () => void;
}

export default function ConversationSidebar({ 
  selectedConversation, 
  onSelectConversation, 
  onNewConversation 
}: ConversationSidebarProps) {
  const [conversations, setConversations] = useState<ConversationThread[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [creating, setCreating] = useState(false);
  const [activeDropdown, setActiveDropdown] = useState<string | null>(null);

  // Form state for creating conversation
  const [newConversation, setNewConversation] = useState<ConversationCreate>({
    title: '',
    description: '',
    character_id: 'default'
  });

  // Load conversations on component mount
  useEffect(() => {
    loadConversations();
  }, [searchQuery]);

  const loadConversations = async () => {
    try {
      setLoading(true);
      const params: any = {};
      if (searchQuery) params.search = searchQuery;
      
      const data = await getConversations(params);
      setConversations(data.threads);
    } catch (error) {
      toast.error(handleApiError(error));
    } finally {
      setLoading(false);
    }
  };

  const handleCreateConversation = async () => {
    if (!newConversation.title.trim()) {
      // Auto-generate title if empty
      const timestamp = new Date().toLocaleString();
      setNewConversation(prev => ({ 
        ...prev, 
        title: `New Conversation ${timestamp}` 
      }));
    }

    try {
      setCreating(true);
      const conversation = await createConversation({
        ...newConversation,
        title: newConversation.title || `New Conversation ${new Date().toLocaleString()}`
      });
      
      setConversations(prev => [conversation, ...prev]);
      setNewConversation({ title: '', description: '', character_id: 'default' });
      setShowCreateForm(false);
      onSelectConversation(conversation);
      toast.success('Conversation created successfully');
    } catch (error) {
      toast.error(handleApiError(error));
    } finally {
      setCreating(false);
    }
  };

  const handleQuickNewConversation = async () => {
    try {
      setCreating(true);
      const timestamp = new Date().toLocaleString();
      const conversation = await createConversation({
        title: `New Conversation ${timestamp}`,
        character_id: 'default'
      });
      
      setConversations(prev => [conversation, ...prev]);
      onSelectConversation(conversation);
      onNewConversation();
      toast.success('New conversation started');
    } catch (error) {
      toast.error(handleApiError(error));
    } finally {
      setCreating(false);
    }
  };

  const handleTogglePin = async (conversation: ConversationThread, e: React.MouseEvent) => {
    e.stopPropagation();
    try {
      const updated = await updateConversation(conversation.id, {
        is_pinned: !conversation.is_pinned
      });
      setConversations(prev => 
        prev.map(c => c.id === conversation.id ? updated : c)
      );
      toast.success(updated.is_pinned ? 'Conversation pinned' : 'Conversation unpinned');
    } catch (error) {
      toast.error(handleApiError(error));
    }
  };

  const handleDeleteConversation = async (conversation: ConversationThread, e: React.MouseEvent) => {
    e.stopPropagation();
    if (!confirm('Are you sure you want to delete this conversation?')) return;

    try {
      await deleteConversation(conversation.id);
      setConversations(prev => prev.filter(c => c.id !== conversation.id));
      
      // If deleted conversation was selected, clear selection
      if (selectedConversation?.id === conversation.id) {
        onSelectConversation(conversations[0] || null);
      }
      
      toast.success('Conversation deleted');
    } catch (error) {
      toast.error(handleApiError(error));
    }
  };

  const formatLastActivity = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);
    
    if (diffInHours < 1) {
      return 'Just now';
    } else if (diffInHours < 24) {
      return `${Math.floor(diffInHours)}h ago`;
    } else {
      return date.toLocaleDateString();
    }
  };

  // Sort conversations: pinned first, then by last activity
  const sortedConversations = [...conversations].sort((a, b) => {
    if (a.is_pinned && !b.is_pinned) return -1;
    if (!a.is_pinned && b.is_pinned) return 1;
    return new Date(b.last_activity_at).getTime() - new Date(a.last_activity_at).getTime();
  });

  return (
    <div className="w-80 bg-gray-50 border-r border-gray-200 flex flex-col h-full">
      {/* Header */}
      <div className="p-4 border-b border-gray-200 bg-white">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold text-gray-900">Conversations</h2>
          <button
            onClick={handleQuickNewConversation}
            disabled={creating}
            className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
            title="New Conversation"
          >
            <Plus className="w-5 h-5" />
          </button>
        </div>

        {/* Search */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <input
            type="text"
            placeholder="Search conversations..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
          />
        </div>
      </div>

      {/* Conversations List */}
      <div className="flex-1 overflow-y-auto">
        {loading ? (
          <div className="p-4 text-center text-gray-500">
            Loading conversations...
          </div>
        ) : sortedConversations.length === 0 ? (
          <div className="p-4 text-center text-gray-500">
            <MessageCircle className="w-12 h-12 text-gray-300 mx-auto mb-2" />
            <p className="text-sm">No conversations yet</p>
            <button
              onClick={() => setShowCreateForm(true)}
              className="mt-2 text-blue-600 hover:text-blue-700 text-sm"
            >
              Start your first conversation
            </button>
          </div>
        ) : (
          <div className="p-2">
            {sortedConversations.map((conversation) => (
              <div
                key={conversation.id}
                onClick={() => onSelectConversation(conversation)}
                className={`relative p-3 rounded-lg cursor-pointer transition-colors mb-2 group ${
                  selectedConversation?.id === conversation.id
                    ? 'bg-blue-100 border border-blue-200'
                    : 'bg-white hover:bg-gray-50 border border-transparent'
                }`}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2">
                      {conversation.is_pinned && (
                        <Pin className="w-3 h-3 text-blue-500 flex-shrink-0" />
                      )}
                      <h3 className="text-sm font-medium text-gray-900 truncate">
                        {conversation.title}
                      </h3>
                    </div>
                    
                    {conversation.description && (
                      <p className="text-xs text-gray-500 mt-1 line-clamp-2">
                        {conversation.description}
                      </p>
                    )}
                    
                    <div className="flex items-center justify-between mt-2">
                      <span className="text-xs text-gray-400">
                        {conversation.message_count} messages
                      </span>
                      <span className="text-xs text-gray-400">
                        {formatLastActivity(conversation.last_activity_at)}
                      </span>
                    </div>
                  </div>

                  {/* Dropdown Menu */}
                  <div className="relative">
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        setActiveDropdown(activeDropdown === conversation.id ? null : conversation.id);
                      }}
                      className="p-1 text-gray-400 hover:text-gray-600 opacity-0 group-hover:opacity-100 transition-opacity"
                    >
                      <MoreVertical className="w-4 h-4" />
                    </button>

                    {activeDropdown === conversation.id && (
                      <div className="absolute right-0 top-6 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-10">
                        <button
                          onClick={(e) => handleTogglePin(conversation, e)}
                          className="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 flex items-center"
                        >
                          {conversation.is_pinned ? (
                            <>
                              <PinOff className="w-4 h-4 mr-2" />
                              Unpin
                            </>
                          ) : (
                            <>
                              <Pin className="w-4 h-4 mr-2" />
                              Pin
                            </>
                          )}
                        </button>
                        <button
                          onClick={(e) => handleDeleteConversation(conversation, e)}
                          className="w-full px-4 py-2 text-left text-sm text-red-600 hover:bg-red-50 flex items-center"
                        >
                          <Trash2 className="w-4 h-4 mr-2" />
                          Delete
                        </button>
                      </div>
                    )}
                  </div>
                </div>

                {/* Status indicator */}
                <div className={`absolute top-2 right-2 w-2 h-2 rounded-full ${
                  conversation.status === ConversationStatus.ACTIVE ? 'bg-green-400' :
                  conversation.status === ConversationStatus.PAUSED ? 'bg-yellow-400' :
                  'bg-gray-400'
                }`} />
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Create Conversation Form */}
      {showCreateForm && (
        <div className="absolute inset-0 bg-white z-20">
          <div className="p-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold">New Conversation</h3>
              <button
                onClick={() => setShowCreateForm(false)}
                className="p-1 text-gray-400 hover:text-gray-600"
              >
                <X className="w-5 h-5" />
              </button>
            </div>
          </div>

          <div className="p-4 space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Title
              </label>
              <input
                type="text"
                value={newConversation.title}
                onChange={(e) => setNewConversation(prev => ({ ...prev, title: e.target.value }))}
                placeholder="Enter conversation title..."
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Description (optional)
              </label>
              <textarea
                value={newConversation.description}
                onChange={(e) => setNewConversation(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Describe what this conversation is about..."
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            <div className="flex space-x-3">
              <button
                onClick={handleCreateConversation}
                disabled={creating}
                className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
              >
                {creating ? 'Creating...' : 'Create'}
              </button>
              <button
                onClick={() => setShowCreateForm(false)}
                className="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Click outside to close dropdown */}
      {activeDropdown && (
        <div
          className="fixed inset-0 z-5"
          onClick={() => setActiveDropdown(null)}
        />
      )}
    </div>
  );
}
