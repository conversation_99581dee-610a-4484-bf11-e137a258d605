'use client';

import { useState, useEffect } from 'react';
import { getExampleRequests } from '@/lib/api';
import { ExampleRequest } from '@/types/motion';
import { Lightbulb, Copy, CheckCircle } from 'lucide-react';

interface ExampleRequestsProps {
  onSelectExample: (text: string) => void;
}

export default function ExampleRequests({ onSelectExample }: ExampleRequestsProps) {
  const [examples, setExamples] = useState<{
    simple_examples: ExampleRequest[];
    intermediate_examples: ExampleRequest[];
    advanced_examples: ExampleRequest[];
  } | null>(null);
  const [loading, setLoading] = useState(true);
  const [copiedIndex, setCopiedIndex] = useState<string | null>(null);

  useEffect(() => {
    const fetchExamples = async () => {
      try {
        const data = await getExampleRequests();
        setExamples(data);
      } catch (error) {
        console.error('Failed to fetch examples:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchExamples();
  }, []);

  const handleCopyExample = async (text: string, index: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedIndex(index);
      setTimeout(() => setCopiedIndex(null), 2000);
    } catch (error) {
      console.error('Failed to copy text:', error);
    }
  };

  const handleSelectExample = (text: string) => {
    onSelectExample(text);
  };

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex items-center gap-2 mb-4">
          <Lightbulb className="w-5 h-5 text-yellow-500" />
          <h3 className="text-lg font-semibold">Example Requests</h3>
        </div>
        <div className="animate-pulse space-y-3">
          {[1, 2, 3].map((i) => (
            <div key={i} className="h-16 bg-gray-200 rounded"></div>
          ))}
        </div>
      </div>
    );
  }

  if (!examples) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex items-center gap-2 mb-4">
          <Lightbulb className="w-5 h-5 text-yellow-500" />
          <h3 className="text-lg font-semibold">Example Requests</h3>
        </div>
        <p className="text-gray-500">Failed to load examples</p>
      </div>
    );
  }

  const renderExampleGroup = (title: string, exampleList: ExampleRequest[], prefix: string) => (
    <div className="mb-6">
      <h4 className="text-md font-medium text-gray-700 mb-3">{title}</h4>
      <div className="space-y-2">
        {exampleList.map((example, index) => {
          const uniqueId = `${prefix}-${index}`;
          return (
            <div
              key={uniqueId}
              className="border border-gray-200 rounded-lg p-3 hover:border-blue-300 transition-colors cursor-pointer group"
              onClick={() => handleSelectExample(example.text)}
            >
              <div className="flex justify-between items-start">
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-900 group-hover:text-blue-600">
                    {example.text}
                  </p>
                  <p className="text-xs text-gray-500 mt-1">{example.description}</p>
                  <span className="inline-block mt-2 px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded">
                    {example.animator_level}
                  </span>
                </div>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleCopyExample(example.text, uniqueId);
                  }}
                  className="ml-2 p-1 text-gray-400 hover:text-gray-600 transition-colors"
                  title="Copy to clipboard"
                >
                  {copiedIndex === uniqueId ? (
                    <CheckCircle className="w-4 h-4 text-green-500" />
                  ) : (
                    <Copy className="w-4 h-4" />
                  )}
                </button>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <div className="flex items-center gap-2 mb-6">
        <Lightbulb className="w-5 h-5 text-yellow-500" />
        <h3 className="text-lg font-semibold">Example Requests</h3>
      </div>

      {renderExampleGroup('Simple Examples', examples.simple_examples, 'simple')}
      {renderExampleGroup('Intermediate Examples', examples.intermediate_examples, 'intermediate')}
      {renderExampleGroup('Advanced Examples', examples.advanced_examples, 'advanced')}

      <div className="mt-4 p-3 bg-blue-50 rounded-lg">
        <p className="text-sm text-blue-700">
          💡 Click on any example to use it as your input, or copy it to modify as needed.
        </p>
      </div>
    </div>
  );
}
