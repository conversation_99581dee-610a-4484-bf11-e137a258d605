'use client';

import { useState } from 'react';
import { AnimationResponse } from '@/types/motion';
import {
  CheckCircle,
  Download,
  Play,
  Pause,
  RotateCcw,
  Eye,
  FileText,
  Clock,
  Zap,
  AlertTriangle,
  Monitor
} from 'lucide-react';
import toast from 'react-hot-toast';
import Animation3DPreview from './Animation3DPreview';

interface ChatAnimationPreviewProps {
  response: AnimationResponse;
  onRegenerate?: () => void;
}

export default function ChatAnimationPreview({ response, onRegenerate }: ChatAnimationPreviewProps) {
  const [isPreviewOpen, setIsPreviewOpen] = useState(false);
  const [downloading, setDownloading] = useState(false);
  const [is3DPreviewOpen, setIs3DPreviewOpen] = useState(false);

  const handleDownload = async () => {
    if (!response.fbx_file_path) {
      toast.error('No FBX file available for download');
      return;
    }

    try {
      setDownloading(true);
      
      // Extract filename from the full path
      const filename = response.fbx_file_path.split('/').pop() || 'animation.fbx';

      // Create download URL using the same base URL as the API
      const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:9000';
      const downloadUrl = `${API_BASE_URL}/animation/download/${filename}`;

      toast.loading('Downloading FBX file...', { id: 'download' });

      // Fetch the file
      const downloadResponse = await fetch(downloadUrl);

      if (!downloadResponse.ok) {
        throw new Error(`Download failed: ${downloadResponse.statusText}`);
      }

      // Create blob and download
      const blob = await downloadResponse.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      toast.success('FBX file downloaded successfully!', { id: 'download' });
    } catch (error) {
      console.error('Download error:', error);
      toast.error(`Download failed: ${error instanceof Error ? error.message : 'Unknown error'}`, { id: 'download' });
    } finally {
      setDownloading(false);
    }
  };

  const formatDuration = (seconds: number) => {
    if (seconds < 60) {
      return `${seconds.toFixed(1)}s`;
    }
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds.toFixed(1)}s`;
  };

  return (
    <div className="bg-gradient-to-br from-green-50 to-blue-50 border border-green-200 rounded-lg p-4 mt-3">
      {/* Header */}
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center space-x-2">
          <CheckCircle className="w-5 h-5 text-green-600" />
          <span className="text-sm font-medium text-green-800">Animation Generated</span>
        </div>
        <div className="flex items-center space-x-1">
          <button
            onClick={() => setIsPreviewOpen(!isPreviewOpen)}
            className="p-1 text-blue-600 hover:bg-blue-100 rounded transition-colors"
            title="Toggle Preview"
          >
            <Eye className="w-4 h-4" />
          </button>
          {onRegenerate && (
            <button
              onClick={onRegenerate}
              className="p-1 text-gray-600 hover:bg-gray-100 rounded transition-colors"
              title="Regenerate"
            >
              <RotateCcw className="w-4 h-4" />
            </button>
          )}
        </div>
      </div>

      {/* Quick Info */}
      <div className="grid grid-cols-2 gap-3 mb-3">
        {response.animation_sequence && (
          <>
            <div className="flex items-center space-x-2">
              <Clock className="w-4 h-4 text-gray-500" />
              <span className="text-sm text-gray-700">
                {formatDuration(response.animation_sequence.total_duration)}
              </span>
            </div>
            <div className="flex items-center space-x-2">
              <Zap className="w-4 h-4 text-gray-500" />
              <span className="text-sm text-gray-700">
                {response.animation_sequence.frame_rate} FPS
              </span>
            </div>
          </>
        )}
      </div>

      {/* Actions */}
      <div className="flex items-center space-x-2">
        {response.fbx_file_path && (
          <>
            <button
              onClick={() => setIs3DPreviewOpen(true)}
              className="flex items-center space-x-1 px-3 py-1.5 bg-blue-600 text-white text-sm rounded hover:bg-blue-700 transition-colors"
            >
              <Monitor className="w-4 h-4" />
              <span>3D Preview</span>
            </button>

            <button
              onClick={handleDownload}
              disabled={downloading}
              className="flex items-center space-x-1 px-3 py-1.5 bg-green-600 text-white text-sm rounded hover:bg-green-700 disabled:opacity-50 transition-colors"
            >
              <Download className="w-4 h-4" />
              <span>{downloading ? 'Downloading...' : 'Download FBX'}</span>
            </button>
          </>
        )}

        {response.animation_name && (
          <span className="text-xs text-gray-600 bg-white px-2 py-1 rounded">
            {response.animation_name}
          </span>
        )}
      </div>

      {/* Expanded Preview */}
      {isPreviewOpen && (
        <div className="mt-4 pt-4 border-t border-green-200">
          {/* Animation Sequence Details */}
          {response.animation_sequence && (
            <div className="mb-4">
              <h4 className="text-sm font-medium text-gray-900 mb-2">Animation Details</h4>
              <div className="bg-white rounded p-3 space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Duration:</span>
                  <span className="font-medium">{formatDuration(response.animation_sequence.total_duration)}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Frame Rate:</span>
                  <span className="font-medium">{response.animation_sequence.frame_rate} FPS</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Total Frames:</span>
                  <span className="font-medium">{response.animation_sequence.total_frames}</span>
                </div>
              </div>
            </div>
          )}

          {/* Processed Actions */}
          {response.processed_actions && response.processed_actions.length > 0 && (
            <div className="mb-4">
              <h4 className="text-sm font-medium text-gray-900 mb-2">Processed Actions</h4>
              <div className="flex flex-wrap gap-1">
                {response.processed_actions.map((action, index) => (
                  <span
                    key={index}
                    className="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs"
                  >
                    {action}
                  </span>
                ))}
              </div>
            </div>
          )}

          {/* File Information */}
          {response.fbx_file_path && (
            <div className="mb-4">
              <h4 className="text-sm font-medium text-gray-900 mb-2">File Information</h4>
              <div className="bg-white rounded p-3">
                <div className="flex items-center space-x-2">
                  <FileText className="w-4 h-4 text-gray-500" />
                  <span className="text-sm text-gray-700 font-mono">
                    {response.fbx_file_path.split('/').pop()}
                  </span>
                </div>
              </div>
            </div>
          )}

          {/* Quality Report */}
          {response.quality_report && Object.keys(response.quality_report).length > 0 && (
            <div className="mb-4">
              <h4 className="text-sm font-medium text-gray-900 mb-2">Quality Report</h4>
              <div className="bg-white rounded p-3">
                <pre className="text-xs text-gray-600 overflow-x-auto">
                  {JSON.stringify(response.quality_report, null, 2)}
                </pre>
              </div>
            </div>
          )}

          {/* Warnings */}
          {response.warnings && response.warnings.length > 0 && (
            <div className="mb-4">
              <h4 className="text-sm font-medium text-gray-900 mb-2 flex items-center space-x-1">
                <AlertTriangle className="w-4 h-4 text-yellow-500" />
                <span>Warnings</span>
              </h4>
              <div className="bg-yellow-50 border border-yellow-200 rounded p-3">
                <ul className="space-y-1">
                  {response.warnings.map((warning, index) => (
                    <li key={index} className="text-xs text-yellow-800">
                      • {warning}
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          )}

          {/* Processing Time */}
          {response.processing_time && (
            <div className="text-center">
              <span className="text-xs text-gray-500">
                Processed in {response.processing_time.toFixed(2)}s
              </span>
            </div>
          )}
        </div>
      )}

      {/* 3D Preview Modal */}
      <Animation3DPreview
        fbxUrl={response.fbx_file_path ? `${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:9000'}/animation/download/${response.fbx_file_path.split('/').pop()}` : undefined}
        animationName={response.animation_name}
        isOpen={is3DPreviewOpen}
        onClose={() => setIs3DPreviewOpen(false)}
      />
    </div>
  );
}
