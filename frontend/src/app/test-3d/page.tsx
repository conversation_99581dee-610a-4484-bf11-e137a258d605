'use client';

import { useState } from 'react';
import Animation3DPreview from '@/components/animation-3d-preview';

export default function Test3DPage() {
  const [isPreviewOpen, setIsPreviewOpen] = useState(false);
  const [fbxUrl, setFbxUrl] = useState('');

  const handleOpenPreview = () => {
    if (fbxUrl.trim()) {
      setIsPreviewOpen(true);
    } else {
      alert('Please enter a valid FBX URL');
    }
  };

  return (
    <div className="min-h-screen bg-gray-100 p-8">
      <div className="max-w-2xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">
          3D Animation Preview Test
        </h1>
        
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold mb-4">Test FBX Animation</h2>
          
          <div className="space-y-4">
            <div>
              <label htmlFor="fbx-url" className="block text-sm font-medium text-gray-700 mb-2">
                FBX File URL
              </label>
              <input
                id="fbx-url"
                type="url"
                value={fbxUrl}
                onChange={(e) => setFbxUrl(e.target.value)}
                placeholder="https://example.com/animation.fbx"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <p className="text-xs text-gray-500 mt-1">
                Enter a URL to an FBX file with animations
              </p>
            </div>
            
            <button
              onClick={handleOpenPreview}
              className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              Open 3D Preview
            </button>
          </div>
          
          <div className="mt-6 p-4 bg-gray-50 rounded-md">
            <h3 className="font-medium text-gray-900 mb-2">Features:</h3>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• Three.js powered 3D rendering</li>
              <li>• FBX model and animation loading</li>
              <li>• Interactive camera controls (orbit, zoom, pan)</li>
              <li>• Animation playback controls</li>
              <li>• Progress bar with time scrubbing</li>
              <li>• Professional lighting and environment</li>
              <li>• Performance statistics (optional)</li>
            </ul>
          </div>
          
          <div className="mt-4 p-4 bg-yellow-50 rounded-md">
            <h3 className="font-medium text-yellow-800 mb-2">Note:</h3>
            <p className="text-sm text-yellow-700">
              Make sure the FBX file is accessible via CORS and contains animation data.
              For testing, you can use sample FBX files from online repositories.
            </p>
          </div>
        </div>
      </div>
      
      <Animation3DPreview
        fbxUrl={fbxUrl}
        animationName="Test Animation"
        isOpen={isPreviewOpen}
        onClose={() => setIsPreviewOpen(false)}
      />
    </div>
  );
}
