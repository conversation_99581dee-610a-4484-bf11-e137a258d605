'use client';

import { useState, useEffect } from 'react';
import { Toaster } from 'react-hot-toast';
import Navigation from '@/components/navigation';
import MotionGenerator from '@/components/motion-generator';
import MotionGeneratorAdvanced from '@/components/motion-generator-advanced';
import AnimationPreview from '@/components/animation-preview';
import ExampleRequests from '@/components/example-requests';
import ConversationManager from '@/components/conversation-manager';
import TaskMonitor from '@/components/task-monitor';
import StatusDashboard from '@/components/status-dashboard';
import ChatInterface from '@/components/chat-interface';
import ConversationSidebar from '@/components/conversation-sidebar';
import { AnimationResponse, HealthStatus, ConversationThread } from '@/types/motion';
import { checkHealth, createConversation } from '@/lib/api';
import { Activity, AlertCircle, CheckCircle } from 'lucide-react';

export default function Home() {
  const [currentPage, setCurrentPage] = useState('chat');
  const [response, setResponse] = useState<AnimationResponse | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedExample, setSelectedExample] = useState<string>('');
  const [backendStatus, setBackendStatus] = useState<'checking' | 'online' | 'offline'>('checking');
  const [healthData, setHealthData] = useState<HealthStatus | null>(null);
  const [selectedConversation, setSelectedConversation] = useState<ConversationThread | null>(null);
  const [autoCreateConversation, setAutoCreateConversation] = useState(true);

  // Check backend health on component mount
  useEffect(() => {
    const checkBackendHealth = async () => {
      try {
        const health = await checkHealth();
        setHealthData(health);
        setBackendStatus('online');
      } catch (error) {
        console.error('Backend health check failed:', error);
        setBackendStatus('offline');
      }
    };

    checkBackendHealth();
    // Check health every 30 seconds
    const interval = setInterval(checkBackendHealth, 30000);
    return () => clearInterval(interval);
  }, []);

  // Auto-create a new conversation when the app loads (ChatGPT style)
  useEffect(() => {
    if (autoCreateConversation && backendStatus === 'online' && !selectedConversation) {
      createDefaultConversation();
    }
  }, [backendStatus, autoCreateConversation]);

  const createDefaultConversation = async () => {
    try {
      const timestamp = new Date().toLocaleString();
      const conversation = await createConversation({
        title: `New Conversation ${timestamp}`,
        character_id: 'default'
      });
      setSelectedConversation(conversation);
      setAutoCreateConversation(false);
    } catch (error) {
      console.error('Failed to create default conversation:', error);
    }
  };

  const handleExampleSelect = (text: string) => {
    setSelectedExample(text);
    // Clear previous results when selecting new example
    setResponse(null);
    setError(null);
  };

  const handlePageChange = (page: string) => {
    setCurrentPage(page);
  };

  const handleConversationSelect = (conversation: ConversationThread) => {
    setSelectedConversation(conversation);
  };

  const handleNewConversation = () => {
    // This will be called when a new conversation is created
    // The conversation will be automatically selected in the sidebar
  };



  const renderPageContent = () => {
    switch (currentPage) {
      case 'chat':
        return (
          <div className="flex h-[calc(100vh-80px)]">
            <ConversationSidebar
              selectedConversation={selectedConversation}
              onSelectConversation={handleConversationSelect}
              onNewConversation={handleNewConversation}
            />
            <ChatInterface
              conversation={selectedConversation}
              onConversationUpdate={handleConversationSelect}
            />
          </div>
        );

      case 'home':
        return (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Left Column - Input and Examples */}
            <div className="space-y-8">
              <MotionGenerator
                onResponse={setResponse}
                onLoading={setLoading}
                onError={setError}
                selectedExample={selectedExample}
              />
              <ExampleRequests onSelectExample={handleExampleSelect} />
            </div>

            {/* Right Column - Preview */}
            <div>
              <AnimationPreview
                response={response}
                loading={loading}
                error={error}
              />
            </div>
          </div>
        );

      case 'motion':
        return (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <div>
              <MotionGeneratorAdvanced />
            </div>
            <div>
              <AnimationPreview
                response={response}
                loading={loading}
                error={error}
              />
            </div>
          </div>
        );

      case 'conversations':
        return (
          <div className="h-[calc(100vh-200px)]">
            <ConversationManager onSelectConversation={handleConversationSelect} />
          </div>
        );

      case 'tasks':
        return (
          <div className="h-[calc(100vh-200px)]">
            <TaskMonitor conversationId={selectedConversation?.id} />
          </div>
        );

      case 'status':
        return <StatusDashboard />;

      default:
        return (
          <div className="text-center py-12">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Page Not Found</h2>
            <p className="text-gray-600">The requested page could not be found.</p>
          </div>
        );
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Toaster position="top-right" />

      {/* Navigation - Only show for non-chat pages */}
      {currentPage !== 'chat' && (
        <Navigation
          currentPage={currentPage}
          onPageChange={handlePageChange}
          backendStatus={backendStatus}
          healthData={healthData}
        />
      )}

      {/* Chat Page - Full Screen Layout */}
      {currentPage === 'chat' ? (
        <div className="h-screen flex flex-col">
          {/* Simple header for chat mode */}
          <div className="bg-white border-b border-gray-200 px-4 py-3">
            <div className="flex items-center justify-between">
              <h1 className="text-xl font-semibold text-gray-900">Motion Agent</h1>
              <div className="flex items-center space-x-4">
                <button
                  onClick={() => setCurrentPage('home')}
                  className="text-sm text-blue-600 hover:text-blue-700"
                >
                  Other Tools
                </button>
                <div className={`flex items-center space-x-2 ${
                  backendStatus === 'online' ? 'text-green-600' :
                  backendStatus === 'offline' ? 'text-red-600' : 'text-yellow-600'
                }`}>
                  {backendStatus === 'online' ? (
                    <CheckCircle className="w-4 h-4" />
                  ) : backendStatus === 'offline' ? (
                    <AlertCircle className="w-4 h-4" />
                  ) : (
                    <Activity className="w-4 h-4 animate-pulse" />
                  )}
                  <span className="text-sm capitalize">{backendStatus}</span>
                </div>
              </div>
            </div>
          </div>
          {renderPageContent()}
        </div>
      ) : (
        /* Other Pages - Standard Layout */
        <>
          {/* Main Content */}
          <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            {renderPageContent()}

            {/* Backend Status Details - Only show on home page */}
            {currentPage === 'home' && healthData && backendStatus === 'online' && (
              <div className="mt-8 bg-white rounded-lg shadow-md p-6">
                <h3 className="text-lg font-semibold mb-4">Backend Status</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  <div className="text-center p-3 bg-green-50 rounded-lg">
                    <div className="text-sm text-gray-600">NLU Pipeline</div>
                    <div className="font-semibold text-green-700">
                      {healthData.nlu_pipeline || 'Ready'}
                    </div>
                  </div>
                  <div className="text-center p-3 bg-blue-50 rounded-lg">
                    <div className="text-sm text-gray-600">LangGraph</div>
                    <div className="font-semibold text-blue-700">
                      {healthData.langgraph_pipeline || 'Ready'}
                    </div>
                  </div>
                  <div className="text-center p-3 bg-purple-50 rounded-lg">
                    <div className="text-sm text-gray-600">Professional Animator</div>
                    <div className="font-semibold text-purple-700">
                      {healthData.professional_animator || 'Ready'}
                    </div>
                  </div>
                  <div className="text-center p-3 bg-orange-50 rounded-lg">
                    <div className="text-sm text-gray-600">Version</div>
                    <div className="font-semibold text-orange-700">{healthData.version}</div>
                  </div>
                </div>
              </div>
            )}
          </main>

          {/* Footer */}
          <footer className="bg-white border-t mt-16">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
              <div className="text-center text-gray-600">
                <p>Motion Agent - Professional 3D Animation Generator</p>
                <p className="text-sm mt-2">
                  Powered by MotionGPT + Motion Agent + MoDi + Blender + Python
                </p>
              </div>
            </div>
          </footer>
        </>
      )}
    </div>
  );
}
