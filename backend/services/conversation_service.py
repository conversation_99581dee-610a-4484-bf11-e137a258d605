"""
对话管理服务
Conversation Management Service
"""

from datetime import datetime
from typing import Optional

from beanie import PydanticObjectId
from loguru import logger
from pymongo import DESCENDING

from ..models.conversation import (
    ConversationStatus,
    ConversationThread,
    ConversationThreadCreate,
    ConversationThreadFilter,
    ConversationThreadList,
    ConversationThreadResponse,
    ConversationThreadUpdate,
)
from ..models.message import ConversationHistory, Message, MessageResponse


class ConversationService:
    """对话管理服务"""

    def __init__(self):
        pass

    async def create_conversation(
        self,
        conversation_data: ConversationThreadCreate
    ) -> ConversationThreadResponse:
        """创建新对话"""
        try:
            # 创建对话线程
            conversation = ConversationThread(
                title=conversation_data.title,
                description=conversation_data.description,
                character_id=conversation_data.character_id,
                user_id=conversation_data.user_id,
                session_id=conversation_data.session_id,
                context=conversation_data.context,
                settings=conversation_data.settings,
                status=ConversationStatus.ACTIVE,
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow(),
                last_activity_at=datetime.utcnow(),
            )

            await conversation.insert()

            logger.info(f"Created new conversation: {conversation.id}")

            return ConversationThreadResponse.from_document(conversation)

        except Exception as e:
            logger.error(f"Failed to create conversation: {e}")
            raise

    async def get_conversation(self, conversation_id: str) -> ConversationThreadResponse | None:
        """获取对话详情"""
        try:
            conversation = await ConversationThread.get(
                PydanticObjectId(conversation_id),
                fetch_links=False
            )

            if not conversation or conversation.is_deleted:
                return None

            return ConversationThreadResponse.from_document(conversation)

        except Exception as e:
            logger.error(f"Failed to get conversation {conversation_id}: {e}")
            raise

    async def update_conversation(
        self,
        conversation_id: str,
        update_data: ConversationThreadUpdate
    ) -> ConversationThreadResponse | None:
        """更新对话"""
        try:
            conversation = await ConversationThread.get(
                PydanticObjectId(conversation_id),
                fetch_links=False
            )

            if not conversation or conversation.is_deleted:
                return None

            # 更新字段
            update_dict = update_data.model_dump(exclude_unset=True)
            for field, value in update_dict.items():
                setattr(conversation, field, value)

            conversation.updated_at = datetime.utcnow()

            await conversation.save()

            logger.info(f"Updated conversation: {conversation_id}")

            return ConversationThreadResponse.from_document(conversation)

        except Exception as e:
            logger.error(f"Failed to update conversation {conversation_id}: {e}")
            raise

    async def delete_conversation(self, conversation_id: str) -> bool:
        """删除对话（软删除）"""
        try:
            conversation = await ConversationThread.get(
                PydanticObjectId(conversation_id),
                fetch_links=False
            )

            if not conversation or conversation.is_deleted:
                return False

            conversation.is_deleted = True
            conversation.updated_at = datetime.utcnow()

            await conversation.save()

            logger.info(f"Deleted conversation: {conversation_id}")
            return True

        except Exception as e:
            logger.error(f"Failed to delete conversation {conversation_id}: {e}")
            raise

    async def list_conversations(
        self,
        filter_params: ConversationThreadFilter
    ) -> ConversationThreadList:
        """获取对话列表"""
        try:
            # 构建查询条件
            query_filter = {"is_deleted": False}

            if filter_params.status:
                query_filter["status"] = filter_params.status

            if filter_params.user_id:
                query_filter["user_id"] = filter_params.user_id

            if filter_params.character_id:
                query_filter["character_id"] = filter_params.character_id

            if filter_params.is_pinned is not None:
                query_filter["is_pinned"] = filter_params.is_pinned

            # 构建查询
            query = ConversationThread.find(query_filter)

            # 添加文本搜索
            if filter_params.search:
                # 使用 MongoDB 的文本搜索或正则表达式
                search_filter = {
                    "$or": [
                        {"title": {"$regex": filter_params.search, "$options": "i"}},
                        {"description": {"$regex": filter_params.search, "$options": "i"}}
                    ]
                }
                query = ConversationThread.find({"$and": [query_filter, search_filter]})

            # 计算总数
            total = await query.count()

            # 构建排序
            sort_field = filter_params.sort_by
            sort_direction = DESCENDING if filter_params.sort_order == "desc" else 1
            query = query.sort([(sort_field, sort_direction)])

            # 分页查询
            offset = (filter_params.page - 1) * filter_params.page_size
            conversations = await query.skip(offset).limit(filter_params.page_size).to_list()

            # 转换为响应模型
            conversation_responses = [
                ConversationThreadResponse.from_document(conv) for conv in conversations
            ]

            # 计算分页信息
            has_next = offset + len(conversations) < total
            has_prev = filter_params.page > 1

            return ConversationThreadList(
                threads=conversation_responses,
                total=total,
                page=filter_params.page,
                page_size=filter_params.page_size,
                has_next=has_next,
                has_prev=has_prev,
            )

        except Exception as e:
            logger.error(f"Failed to list conversations: {e}")
            raise

    async def update_activity(self, conversation_id: str) -> bool:
        """更新对话活动时间"""
        try:
            conversation = await ConversationThread.get(
                PydanticObjectId(conversation_id),
                fetch_links=False
            )

            if not conversation or conversation.is_deleted:
                return False

            conversation.update_activity()
            await conversation.save()

            return True

        except Exception as e:
            logger.error(f"Failed to update activity for conversation {conversation_id}: {e}")
            raise

    async def get_conversation_history(
        self,
        conversation_id: str,
        limit: int = 100
    ) -> ConversationHistory | None:
        """获取对话历史"""
        try:
            # 获取对话信息
            conversation = await self.get_conversation(conversation_id)
            if not conversation:
                return None

            # 获取消息列表
            messages = await Message.find(
                {
                    "conversation_id": PydanticObjectId(conversation_id),
                    "is_deleted": False
                }
            ).sort([("created_at", 1)]).limit(limit).to_list()

            # 转换为响应模型
            message_responses = [MessageResponse.from_document(msg) for msg in messages]

            # 计算统计信息
            total_tokens = sum(msg.token_count for msg in message_responses if msg.token_count)

            return ConversationHistory(
                conversation_id=conversation_id,
                messages=message_responses,
                total_messages=len(message_responses),
                total_tokens=total_tokens,
                page=1,
                page_size=limit,
                has_next=False,
                has_prev=False,
            )

        except Exception as e:
            logger.error(f"Failed to get conversation history {conversation_id}: {e}")
            raise

    async def update_stats(
        self,
        conversation_id: str,
        message_count_delta: int = 0,
        token_count_delta: int = 0
    ) -> bool:
        """更新对话统计信息"""
        try:
            conversation = await ConversationThread.get(
                PydanticObjectId(conversation_id),
                fetch_links=False
            )

            if not conversation or conversation.is_deleted:
                return False

            # 更新统计
            conversation.update_stats(message_count_delta, token_count_delta)
            await conversation.save()

            logger.debug(f"Updated stats for conversation {conversation_id}: +{message_count_delta} messages, +{token_count_delta} tokens")
            return True

        except Exception as e:
            logger.error(f"Failed to update stats for conversation {conversation_id}: {e}")
            raise
