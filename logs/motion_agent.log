2025-06-10 18:02:08 | INFO     | backend.config:validate_config:305 - Created directory: ./output/animations
2025-06-10 18:02:08 | INFO     | backend.config:validate_config:305 - Created directory: ./temp/animation_data
2025-06-10 18:02:08 | SUCCESS  | backend.config:validate_config:326 - Configuration validation passed
2025-06-10 18:02:08 | INFO     | backend.app:startup_event:109 - Starting Motion Agent API server with Taskiq...
2025-06-10 18:02:08 | INFO     | backend.app:startup_event:112 - Initializing MongoDB database...
2025-06-10 18:02:08 | INFO     | backend.database:init_database:52 - Initializing MongoDB database...
2025-06-10 18:02:08 | SUCCESS  | backend.database:init_database:83 - MongoDB database initialized successfully: motion_agent
2025-06-10 18:02:08 | INFO     | backend.app:startup_event:119 - Creating database indexes...
2025-06-10 18:02:08 | INFO     | backend.database:create_indexes:261 - Creating database indexes...
2025-06-10 18:02:08 | SUCCESS  | backend.database:create_indexes:281 - Database indexes created successfully
2025-06-10 18:02:08 | INFO     | backend.app:startup_event:123 - Initializing taskiq...
2025-06-10 18:02:08 | INFO     | backend.tasks:init_taskiq:89 - Initializing taskiq...
2025-06-10 18:02:08 | SUCCESS  | backend.tasks:init_taskiq:93 - Taskiq initialized successfully
2025-06-10 18:02:08 | SUCCESS  | backend.app:startup_event:133 - Motion Agent API with Taskiq initialized successfully
2025-06-10 18:02:08 | INFO     | backend.app:startup_event:134 - Motion Agent API is ready to serve requests
2025-06-10 18:02:17 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 18:02:18 | INFO     | backend.routers.animation:generate_professional_animation:108 - Received professional animation request: 生成一个 关羽登场 的场景，要求如下：
1. 全景，推镜，黄沙漫天，镜头缓缓推进女主，女主在画面中艰难前行，四周响起从快到慢的战鼓声，顺着声音镜头上摇，遮天蔽日的黄沙逐渐散去，时长 8s,声音 风沙声，战鼓声
2. 全景，缓推镜，风沙逐渐散开后出现伟岸的关羽骑士的轮廓，战鼓声逐渐加强。也出现士兵的呐喊声。时长2时。声音战鼓声，呐喊声
2025-06-10 18:02:18 | INFO     | backend.services.task_service:create_task:74 - Created task: da8c0aee-57bc-444c-9baf-c27ba90a5ab5 (animation_generation)
2025-06-10 18:02:18 | INFO     | backend.services.task_service:create_animation_task:228 - Created animation task: da8c0aee-57bc-444c-9baf-c27ba90a5ab5
2025-06-10 18:02:18 | INFO     | backend.routers.animation:generate_professional_animation:127 - Created animation task: da8c0aee-57bc-444c-9baf-c27ba90a5ab5
2025-06-10 18:02:18 | INFO     | backend.services.task_service:update_task_status:267 - Updated task da8c0aee-57bc-444c-9baf-c27ba90a5ab5 status to running
2025-06-10 18:02:19 | WARNING  | backend.animation.professional_nlu:_load_spacy_model:55 - No spaCy model found, using blank model
2025-06-10 18:02:19 | INFO     | backend.animation.professional_nlu:_load_sentiment_model:64 - Using rule-based sentiment analysis instead of large model
2025-06-10 18:02:19 | INFO     | backend.animation.professional_nlu:_load_action_classifier:74 - Using rule-based action classification instead of large model
2025-06-10 18:02:19 | INFO     | backend.animation.professional_nlu:__init__:41 - Professional Animator NLU initialized
2025-06-10 18:02:19 | INFO     | backend.animation.animator_functions:__init__:37 - Unified Professional Animator initialized
2025-06-10 18:02:19 | INFO     | backend.animation.professional_pipeline:__init__:83 - Professional Animation Pipeline initialized with enhanced features
2025-06-10 18:02:19 | WARNING  | backend.animation.professional_nlu:_load_spacy_model:55 - No spaCy model found, using blank model
2025-06-10 18:02:19 | INFO     | backend.animation.professional_nlu:_load_sentiment_model:64 - Using rule-based sentiment analysis instead of large model
2025-06-10 18:02:19 | INFO     | backend.animation.professional_nlu:_load_action_classifier:74 - Using rule-based action classification instead of large model
2025-06-10 18:02:19 | INFO     | backend.animation.professional_nlu:__init__:41 - Professional Animator NLU initialized
2025-06-10 18:02:19 | INFO     | backend.animation.animator_functions:__init__:37 - Unified Professional Animator initialized
2025-06-10 18:02:19 | INFO     | backend.animation.professional_pipeline:__init__:83 - Professional Animation Pipeline initialized with enhanced features
2025-06-10 18:02:19 | INFO     | backend.animation.professional_pipeline:process_animation_request:92 - Processing animation request: 生成一个 关羽登场 的场景，要求如下：
1. 全景，推镜，黄沙漫天，镜头缓缓推进女主，女主在画面中艰难前行，四周响起从快到慢的战鼓声，顺着声音镜头上摇，遮天蔽日的黄沙逐渐散去，时长 8s,声音 风沙声，战鼓声
2. 全景，缓推镜，风沙逐渐散开后出现伟岸的关羽骑士的轮廓，战鼓声逐渐加强。也出现士兵的呐喊声。时长2时。声音战鼓声，呐喊声
2025-06-10 18:02:19 | INFO     | backend.animation.professional_pipeline:process_animation_request:96 - Step 1: Natural Language Understanding
2025-06-10 18:02:19 | INFO     | backend.animation.professional_nlu:process_natural_language:397 - Processing animation request: 生成一个 关羽登场 的场景，要求如下：
1. 全景，推镜，黄沙漫天，镜头缓缓推进女主，女主在画面中艰难前行，四周响起从快到慢的战鼓声，顺着声音镜头上摇，遮天蔽日的黄沙逐渐散去，时长 8s,声音 风沙声，战鼓声
2. 全景，缓推镜，风沙逐渐散开后出现伟岸的关羽骑士的轮廓，战鼓声逐渐加强。也出现士兵的呐喊声。时长2时。声音战鼓声，呐喊声
2025-06-10 18:02:19 | SUCCESS  | backend.animation.professional_nlu:process_natural_language:426 - Successfully processed 1 actions
2025-06-10 18:02:19 | INFO     | backend.animation.professional_pipeline:process_animation_request:103 - Step 2: Animator Function Processing
2025-06-10 18:02:19 | INFO     | backend.animation.professional_pipeline:_apply_animator_functions:178 - Applying unified animator functions with enhanced features
2025-06-10 18:02:19 | INFO     | backend.animation.animator_functions:create_idle_animation:164 - Creating idle animation: stand
2025-06-10 18:02:19 | INFO     | backend.animation.professional_pipeline:_apply_animator_functions:189 - Optimizing action transitions
2025-06-10 18:02:19 | INFO     | backend.animation.transition_optimizer:optimize_action_sequence:145 - Optimizing sequence of 1 actions...
2025-06-10 18:02:19 | INFO     | backend.animation.professional_pipeline:process_animation_request:109 - Step 3: Generate Blender Animation Data
2025-06-10 18:02:19 | INFO     | backend.animation.professional_pipeline:process_animation_request:113 - Step 4: Execute Blender Animation Generation
2025-06-10 18:02:19 | INFO     | backend.animation.professional_pipeline:_execute_blender_generation:372 - Executing Blender command: /Applications/Blender.app/Contents/MacOS/Blender --background --python blender_scripts/generate_animation.py -- --input temp/animation_data/animation_data_1749549739.json --output output/animations/animation_default_1749549739.fbx --format fbx
2025-06-10 18:02:20 | SUCCESS  | backend.animation.professional_pipeline:_execute_blender_generation:384 - Blender animation generated: output/animations/animation_default_1749549739.fbx
2025-06-10 18:02:20 | WARNING  | backend.animation.professional_pipeline:process_animation_request:118 - Blender generation failed, creating test FBX file
2025-06-10 18:02:20 | INFO     | backend.animation.professional_pipeline:_create_test_fbx_file:569 - Created test FBX file: output/animations/animation_1749549740.fbx
2025-06-10 18:02:20 | INFO     | backend.animation.professional_pipeline:process_animation_request:124 - Step 5: FBX File Validation
2025-06-10 18:02:20 | INFO     | backend.animation.fbx_validator:validate_fbx_file:27 - Starting FBX validation for: output/animations/animation_1749549740.fbx
2025-06-10 18:02:20 | SUCCESS  | backend.animation.fbx_validator:validate_fbx_file:86 - FBX validation completed for: output/animations/animation_1749549740.fbx
2025-06-10 18:02:20 | INFO     | backend.animation.professional_pipeline:process_animation_request:133 - Step 6: Animation Quality Check
2025-06-10 18:02:20 | INFO     | backend.animation.quality_checker:check_animation_quality:130 - Starting animation quality check...
2025-06-10 18:02:20 | SUCCESS  | backend.animation.quality_checker:check_animation_quality:171 - Quality check completed. Overall score: 0.67
2025-06-10 18:02:20 | SUCCESS  | backend.animation.professional_pipeline:process_animation_request:158 - Animation generated successfully in 0.89s
2025-06-10 18:02:20 | INFO     | backend.services.task_service:update_task_status:267 - Updated task da8c0aee-57bc-444c-9baf-c27ba90a5ab5 status to completed
2025-06-10 18:02:20 | INFO     | backend.routers.animation:generate_professional_animation:205 - Updated task da8c0aee-57bc-444c-9baf-c27ba90a5ab5 status to completed
2025-06-10 18:02:20 | SUCCESS  | backend.routers.animation:generate_professional_animation:209 - Professional animation generation completed
2025-06-10 18:02:24 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 18:02:25 | INFO     | backend.routers.animation:download_animation_file:249 - Download request for file: animation_1749549740.fbx
2025-06-10 18:02:25 | INFO     | backend.routers.animation:download_animation_file:264 - Serving file: animation_1749549740.fbx (1212 bytes)
2025-06-10 18:02:54 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 18:03:24 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 18:03:54 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 18:04:51 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 18:05:51 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 18:06:51 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 18:07:47 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 18:07:54 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 18:08:02 | INFO     | backend.app:shutdown_event:140 - Shutting down Motion Agent API...
2025-06-10 18:08:02 | INFO     | backend.tasks:shutdown_taskiq:104 - Shutting down taskiq...
2025-06-10 18:08:02 | SUCCESS  | backend.tasks:shutdown_taskiq:106 - Taskiq shutdown successfully
2025-06-10 18:08:02 | INFO     | backend.database:close_database:96 - Closing MongoDB connections...
2025-06-10 18:08:02 | SUCCESS  | backend.database:close_database:103 - MongoDB connections closed
2025-06-10 18:08:02 | SUCCESS  | backend.app:shutdown_event:148 - Motion Agent API shutdown completed
2025-06-10 21:39:12 | SUCCESS  | backend.config:validate_config:325 - Configuration validation passed
2025-06-10 21:39:12 | INFO     | backend.app:startup_event:110 - Starting Motion Agent API server with Taskiq...
2025-06-10 21:39:12 | INFO     | backend.app:startup_event:113 - Initializing MongoDB database...
2025-06-10 21:39:12 | INFO     | backend.database:init_database:50 - Initializing MongoDB database...
2025-06-10 21:39:12 | SUCCESS  | backend.database:init_database:81 - MongoDB database initialized successfully: motion_agent
2025-06-10 21:39:12 | INFO     | backend.app:startup_event:120 - Creating database indexes...
2025-06-10 21:39:12 | INFO     | backend.database:create_indexes:259 - Creating database indexes...
2025-06-10 21:39:12 | SUCCESS  | backend.database:create_indexes:279 - Database indexes created successfully
2025-06-10 21:39:12 | INFO     | backend.app:startup_event:124 - Initializing taskiq...
2025-06-10 21:39:12 | INFO     | backend.tasks:init_taskiq:89 - Initializing taskiq...
2025-06-10 21:39:12 | SUCCESS  | backend.tasks:init_taskiq:93 - Taskiq initialized successfully
2025-06-10 21:39:12 | SUCCESS  | backend.app:startup_event:134 - Motion Agent API with Taskiq initialized successfully
2025-06-10 21:39:12 | INFO     | backend.app:startup_event:135 - Motion Agent API is ready to serve requests
2025-06-10 21:39:14 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 21:39:39 | INFO     | backend.routers.animation:generate_professional_animation:108 - Received professional animation request: 生成一个 关羽登场 的场景，要求如下：
1. 全景，推镜，黄沙漫天，镜头缓缓推进女主，女主在画面中艰难前行，四周响起从快到慢的战鼓声，顺着声音镜头上摇，遮天蔽日的黄沙逐渐散去，时长 8s,声音 风沙声，战鼓声
2. 全景，缓推镜，风沙逐渐散开后出现伟岸的关羽骑士的轮廓，战鼓声逐渐加强。也出现士兵的呐喊声。时长2时。声音战鼓声，呐喊声
2025-06-10 21:39:39 | INFO     | backend.services.task_service:create_task:69 - Created task: 9fd71306-d94a-4d67-a90d-177ca0bcfc8a (animation_generation)
2025-06-10 21:39:39 | INFO     | backend.services.task_service:create_animation_task:223 - Created animation task: 9fd71306-d94a-4d67-a90d-177ca0bcfc8a
2025-06-10 21:39:39 | INFO     | backend.routers.animation:generate_professional_animation:127 - Created animation task: 9fd71306-d94a-4d67-a90d-177ca0bcfc8a
2025-06-10 21:39:39 | INFO     | backend.services.task_service:update_task_status:262 - Updated task 9fd71306-d94a-4d67-a90d-177ca0bcfc8a status to running
2025-06-10 21:39:41 | WARNING  | backend.animation.professional_nlu:_load_spacy_model:55 - No spaCy model found, using blank model
2025-06-10 21:39:41 | INFO     | backend.animation.professional_nlu:_load_sentiment_model:64 - Using rule-based sentiment analysis instead of large model
2025-06-10 21:39:41 | INFO     | backend.animation.professional_nlu:_load_action_classifier:74 - Using rule-based action classification instead of large model
2025-06-10 21:39:41 | INFO     | backend.animation.professional_nlu:__init__:41 - Professional Animator NLU initialized
2025-06-10 21:39:41 | INFO     | backend.animation.animator_functions:__init__:37 - Unified Professional Animator initialized
2025-06-10 21:39:41 | INFO     | backend.animation.professional_pipeline:__init__:84 - Professional Animation Pipeline initialized with enhanced features
2025-06-10 21:39:41 | WARNING  | backend.animation.professional_nlu:_load_spacy_model:55 - No spaCy model found, using blank model
2025-06-10 21:39:41 | INFO     | backend.animation.professional_nlu:_load_sentiment_model:64 - Using rule-based sentiment analysis instead of large model
2025-06-10 21:39:41 | INFO     | backend.animation.professional_nlu:_load_action_classifier:74 - Using rule-based action classification instead of large model
2025-06-10 21:39:41 | INFO     | backend.animation.professional_nlu:__init__:41 - Professional Animator NLU initialized
2025-06-10 21:39:41 | INFO     | backend.animation.animator_functions:__init__:37 - Unified Professional Animator initialized
2025-06-10 21:39:41 | INFO     | backend.animation.professional_pipeline:__init__:84 - Professional Animation Pipeline initialized with enhanced features
2025-06-10 21:39:41 | INFO     | backend.animation.professional_pipeline:process_animation_request:93 - Processing animation request: 生成一个 关羽登场 的场景，要求如下：
1. 全景，推镜，黄沙漫天，镜头缓缓推进女主，女主在画面中艰难前行，四周响起从快到慢的战鼓声，顺着声音镜头上摇，遮天蔽日的黄沙逐渐散去，时长 8s,声音 风沙声，战鼓声
2. 全景，缓推镜，风沙逐渐散开后出现伟岸的关羽骑士的轮廓，战鼓声逐渐加强。也出现士兵的呐喊声。时长2时。声音战鼓声，呐喊声
2025-06-10 21:39:41 | INFO     | backend.animation.professional_pipeline:process_animation_request:97 - Step 1: Natural Language Understanding
2025-06-10 21:39:41 | INFO     | backend.animation.professional_nlu:process_natural_language:397 - Processing animation request: 生成一个 关羽登场 的场景，要求如下：
1. 全景，推镜，黄沙漫天，镜头缓缓推进女主，女主在画面中艰难前行，四周响起从快到慢的战鼓声，顺着声音镜头上摇，遮天蔽日的黄沙逐渐散去，时长 8s,声音 风沙声，战鼓声
2. 全景，缓推镜，风沙逐渐散开后出现伟岸的关羽骑士的轮廓，战鼓声逐渐加强。也出现士兵的呐喊声。时长2时。声音战鼓声，呐喊声
2025-06-10 21:39:41 | SUCCESS  | backend.animation.professional_nlu:process_natural_language:426 - Successfully processed 1 actions
2025-06-10 21:39:41 | INFO     | backend.animation.professional_pipeline:process_animation_request:104 - Step 2: Animator Function Processing
2025-06-10 21:39:41 | INFO     | backend.animation.professional_pipeline:_apply_animator_functions:179 - Applying unified animator functions with enhanced features
2025-06-10 21:39:41 | INFO     | backend.animation.animator_functions:create_idle_animation:164 - Creating idle animation: stand
2025-06-10 21:39:41 | INFO     | backend.animation.professional_pipeline:_apply_animator_functions:190 - Optimizing action transitions
2025-06-10 21:39:41 | INFO     | backend.animation.transition_optimizer:optimize_action_sequence:146 - Optimizing sequence of 1 actions...
2025-06-10 21:39:41 | INFO     | backend.animation.professional_pipeline:process_animation_request:110 - Step 3: Generate Blender Animation Data
2025-06-10 21:39:41 | INFO     | backend.animation.professional_pipeline:process_animation_request:114 - Step 4: Execute Blender Animation Generation
2025-06-10 21:39:41 | INFO     | backend.animation.professional_pipeline:_execute_blender_generation:373 - Executing Blender command: /Applications/Blender.app/Contents/MacOS/Blender --background --python blender_scripts/generate_animation.py -- --input temp/animation_data/animation_data_1749562781.json --output output/animations/animation_default_1749562781.fbx --format fbx
2025-06-10 21:39:41 | SUCCESS  | backend.animation.professional_pipeline:_execute_blender_generation:385 - Blender animation generated: output/animations/animation_default_1749562781.fbx
2025-06-10 21:39:41 | WARNING  | backend.animation.professional_pipeline:process_animation_request:119 - Blender generation failed, creating test FBX file
2025-06-10 21:39:41 | INFO     | backend.animation.professional_pipeline:_create_test_fbx_file:570 - Created test FBX file: output/animations/animation_1749562781.fbx
2025-06-10 21:39:41 | INFO     | backend.animation.professional_pipeline:process_animation_request:125 - Step 5: FBX File Validation
2025-06-10 21:39:41 | INFO     | backend.animation.fbx_validator:validate_fbx_file:26 - Starting FBX validation for: output/animations/animation_1749562781.fbx
2025-06-10 21:39:41 | SUCCESS  | backend.animation.fbx_validator:validate_fbx_file:85 - FBX validation completed for: output/animations/animation_1749562781.fbx
2025-06-10 21:39:41 | INFO     | backend.animation.professional_pipeline:process_animation_request:134 - Step 6: Animation Quality Check
2025-06-10 21:39:41 | INFO     | backend.animation.quality_checker:check_animation_quality:130 - Starting animation quality check...
2025-06-10 21:39:41 | SUCCESS  | backend.animation.quality_checker:check_animation_quality:171 - Quality check completed. Overall score: 0.67
2025-06-10 21:39:41 | SUCCESS  | backend.animation.professional_pipeline:process_animation_request:159 - Animation generated successfully in 0.85s
2025-06-10 21:39:41 | INFO     | backend.services.task_service:update_task_status:262 - Updated task 9fd71306-d94a-4d67-a90d-177ca0bcfc8a status to completed
2025-06-10 21:39:41 | INFO     | backend.routers.animation:generate_professional_animation:205 - Updated task 9fd71306-d94a-4d67-a90d-177ca0bcfc8a status to completed
2025-06-10 21:39:41 | SUCCESS  | backend.routers.animation:generate_professional_animation:209 - Professional animation generation completed
2025-06-10 21:39:43 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 21:39:44 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 21:39:51 | INFO     | backend.routers.animation:generate_professional_animation:108 - Received professional animation request: 生成一个 关羽登场 的场景，要求如下：
1. 全景，推镜，黄沙漫天，镜头缓缓推进女主，女主在画面中艰难前行，四周响起从快到慢的战鼓声，顺着声音镜头上摇，遮天蔽日的黄沙逐渐散去，时长 8s,声音 风沙声，战鼓声
2. 全景，缓推镜，风沙逐渐散开后出现伟岸的关羽骑士的轮廓，战鼓声逐渐加强。也出现士兵的呐喊声。时长2时。声音战鼓声，呐喊声
2025-06-10 21:39:51 | INFO     | backend.services.task_service:create_task:69 - Created task: d2522261-549b-4358-8d3b-4912599a64a0 (animation_generation)
2025-06-10 21:39:51 | INFO     | backend.services.task_service:create_animation_task:223 - Created animation task: d2522261-549b-4358-8d3b-4912599a64a0
2025-06-10 21:39:51 | INFO     | backend.routers.animation:generate_professional_animation:127 - Created animation task: d2522261-549b-4358-8d3b-4912599a64a0
2025-06-10 21:39:51 | INFO     | backend.services.task_service:update_task_status:262 - Updated task d2522261-549b-4358-8d3b-4912599a64a0 status to running
2025-06-10 21:39:51 | WARNING  | backend.animation.professional_nlu:_load_spacy_model:55 - No spaCy model found, using blank model
2025-06-10 21:39:51 | INFO     | backend.animation.professional_nlu:_load_sentiment_model:64 - Using rule-based sentiment analysis instead of large model
2025-06-10 21:39:51 | INFO     | backend.animation.professional_nlu:_load_action_classifier:74 - Using rule-based action classification instead of large model
2025-06-10 21:39:51 | INFO     | backend.animation.professional_nlu:__init__:41 - Professional Animator NLU initialized
2025-06-10 21:39:51 | INFO     | backend.animation.animator_functions:__init__:37 - Unified Professional Animator initialized
2025-06-10 21:39:51 | INFO     | backend.animation.professional_pipeline:__init__:84 - Professional Animation Pipeline initialized with enhanced features
2025-06-10 21:39:51 | INFO     | backend.animation.professional_pipeline:process_animation_request:93 - Processing animation request: 生成一个 关羽登场 的场景，要求如下：
1. 全景，推镜，黄沙漫天，镜头缓缓推进女主，女主在画面中艰难前行，四周响起从快到慢的战鼓声，顺着声音镜头上摇，遮天蔽日的黄沙逐渐散去，时长 8s,声音 风沙声，战鼓声
2. 全景，缓推镜，风沙逐渐散开后出现伟岸的关羽骑士的轮廓，战鼓声逐渐加强。也出现士兵的呐喊声。时长2时。声音战鼓声，呐喊声
2025-06-10 21:39:51 | INFO     | backend.animation.professional_pipeline:process_animation_request:97 - Step 1: Natural Language Understanding
2025-06-10 21:39:51 | INFO     | backend.animation.professional_nlu:process_natural_language:397 - Processing animation request: 生成一个 关羽登场 的场景，要求如下：
1. 全景，推镜，黄沙漫天，镜头缓缓推进女主，女主在画面中艰难前行，四周响起从快到慢的战鼓声，顺着声音镜头上摇，遮天蔽日的黄沙逐渐散去，时长 8s,声音 风沙声，战鼓声
2. 全景，缓推镜，风沙逐渐散开后出现伟岸的关羽骑士的轮廓，战鼓声逐渐加强。也出现士兵的呐喊声。时长2时。声音战鼓声，呐喊声
2025-06-10 21:39:51 | SUCCESS  | backend.animation.professional_nlu:process_natural_language:426 - Successfully processed 1 actions
2025-06-10 21:39:51 | INFO     | backend.animation.professional_pipeline:process_animation_request:104 - Step 2: Animator Function Processing
2025-06-10 21:39:51 | INFO     | backend.animation.professional_pipeline:_apply_animator_functions:179 - Applying unified animator functions with enhanced features
2025-06-10 21:39:51 | INFO     | backend.animation.animator_functions:create_idle_animation:164 - Creating idle animation: stand
2025-06-10 21:39:51 | INFO     | backend.animation.professional_pipeline:_apply_animator_functions:190 - Optimizing action transitions
2025-06-10 21:39:51 | INFO     | backend.animation.transition_optimizer:optimize_action_sequence:146 - Optimizing sequence of 1 actions...
2025-06-10 21:39:51 | INFO     | backend.animation.professional_pipeline:process_animation_request:110 - Step 3: Generate Blender Animation Data
2025-06-10 21:39:51 | INFO     | backend.animation.professional_pipeline:process_animation_request:114 - Step 4: Execute Blender Animation Generation
2025-06-10 21:39:51 | INFO     | backend.animation.professional_pipeline:_execute_blender_generation:373 - Executing Blender command: /Applications/Blender.app/Contents/MacOS/Blender --background --python blender_scripts/generate_animation.py -- --input temp/animation_data/animation_data_1749562791.json --output output/animations/animation_default_1749562791.fbx --format fbx
2025-06-10 21:39:51 | SUCCESS  | backend.animation.professional_pipeline:_execute_blender_generation:385 - Blender animation generated: output/animations/animation_default_1749562791.fbx
2025-06-10 21:39:51 | WARNING  | backend.animation.professional_pipeline:process_animation_request:119 - Blender generation failed, creating test FBX file
2025-06-10 21:39:51 | INFO     | backend.animation.professional_pipeline:_create_test_fbx_file:570 - Created test FBX file: output/animations/animation_1749562791.fbx
2025-06-10 21:39:51 | INFO     | backend.animation.professional_pipeline:process_animation_request:125 - Step 5: FBX File Validation
2025-06-10 21:39:51 | INFO     | backend.animation.fbx_validator:validate_fbx_file:26 - Starting FBX validation for: output/animations/animation_1749562791.fbx
2025-06-10 21:39:51 | SUCCESS  | backend.animation.fbx_validator:validate_fbx_file:85 - FBX validation completed for: output/animations/animation_1749562791.fbx
2025-06-10 21:39:51 | INFO     | backend.animation.professional_pipeline:process_animation_request:134 - Step 6: Animation Quality Check
2025-06-10 21:39:51 | INFO     | backend.animation.quality_checker:check_animation_quality:130 - Starting animation quality check...
2025-06-10 21:39:51 | SUCCESS  | backend.animation.quality_checker:check_animation_quality:171 - Quality check completed. Overall score: 0.67
2025-06-10 21:39:51 | SUCCESS  | backend.animation.professional_pipeline:process_animation_request:159 - Animation generated successfully in 0.43s
2025-06-10 21:39:51 | INFO     | backend.services.task_service:update_task_status:262 - Updated task d2522261-549b-4358-8d3b-4912599a64a0 status to completed
2025-06-10 21:39:51 | INFO     | backend.routers.animation:generate_professional_animation:205 - Updated task d2522261-549b-4358-8d3b-4912599a64a0 status to completed
2025-06-10 21:39:51 | SUCCESS  | backend.routers.animation:generate_professional_animation:209 - Professional animation generation completed
2025-06-10 21:39:57 | INFO     | backend.routers.animation:download_animation_file:249 - Download request for file: animation_1749562791.fbx
2025-06-10 21:39:57 | INFO     | backend.routers.animation:download_animation_file:264 - Serving file: animation_1749562791.fbx (1212 bytes)
2025-06-10 21:40:14 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 21:40:45 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 21:41:15 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 21:41:45 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 21:42:15 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 21:42:44 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 21:42:46 | INFO     | backend.routers.animation:generate_professional_animation:108 - Received professional animation request: 生成一个 关羽登场 的场景，要求如下：
1. 全景，推镜，黄沙漫天，镜头缓缓推进女主，女主在画面中艰难前行，四周响起从快到慢的战鼓声，顺着声音镜头上摇，遮天蔽日的黄沙逐渐散去，时长 8s,声音 风沙声，战鼓声
2. 全景，缓推镜，风沙逐渐散开后出现伟岸的关羽骑士的轮廓，战鼓声逐渐加强。也出现士兵的呐喊声。时长2时。声音战鼓声，呐喊声
2025-06-10 21:42:46 | INFO     | backend.services.task_service:create_task:69 - Created task: d86f4cfc-1b0d-4c3c-88b3-23ec27c4ca27 (animation_generation)
2025-06-10 21:42:46 | INFO     | backend.services.task_service:create_animation_task:223 - Created animation task: d86f4cfc-1b0d-4c3c-88b3-23ec27c4ca27
2025-06-10 21:42:46 | INFO     | backend.routers.animation:generate_professional_animation:127 - Created animation task: d86f4cfc-1b0d-4c3c-88b3-23ec27c4ca27
2025-06-10 21:42:46 | INFO     | backend.services.task_service:update_task_status:262 - Updated task d86f4cfc-1b0d-4c3c-88b3-23ec27c4ca27 status to running
2025-06-10 21:42:46 | WARNING  | backend.animation.professional_nlu:_load_spacy_model:55 - No spaCy model found, using blank model
2025-06-10 21:42:46 | INFO     | backend.animation.professional_nlu:_load_sentiment_model:64 - Using rule-based sentiment analysis instead of large model
2025-06-10 21:42:46 | INFO     | backend.animation.professional_nlu:_load_action_classifier:74 - Using rule-based action classification instead of large model
2025-06-10 21:42:46 | INFO     | backend.animation.professional_nlu:__init__:41 - Professional Animator NLU initialized
2025-06-10 21:42:46 | INFO     | backend.animation.animator_functions:__init__:37 - Unified Professional Animator initialized
2025-06-10 21:42:46 | INFO     | backend.animation.professional_pipeline:__init__:84 - Professional Animation Pipeline initialized with enhanced features
2025-06-10 21:42:46 | INFO     | backend.animation.professional_pipeline:process_animation_request:93 - Processing animation request: 生成一个 关羽登场 的场景，要求如下：
1. 全景，推镜，黄沙漫天，镜头缓缓推进女主，女主在画面中艰难前行，四周响起从快到慢的战鼓声，顺着声音镜头上摇，遮天蔽日的黄沙逐渐散去，时长 8s,声音 风沙声，战鼓声
2. 全景，缓推镜，风沙逐渐散开后出现伟岸的关羽骑士的轮廓，战鼓声逐渐加强。也出现士兵的呐喊声。时长2时。声音战鼓声，呐喊声
2025-06-10 21:42:46 | INFO     | backend.animation.professional_pipeline:process_animation_request:97 - Step 1: Natural Language Understanding
2025-06-10 21:42:46 | INFO     | backend.animation.professional_nlu:process_natural_language:397 - Processing animation request: 生成一个 关羽登场 的场景，要求如下：
1. 全景，推镜，黄沙漫天，镜头缓缓推进女主，女主在画面中艰难前行，四周响起从快到慢的战鼓声，顺着声音镜头上摇，遮天蔽日的黄沙逐渐散去，时长 8s,声音 风沙声，战鼓声
2. 全景，缓推镜，风沙逐渐散开后出现伟岸的关羽骑士的轮廓，战鼓声逐渐加强。也出现士兵的呐喊声。时长2时。声音战鼓声，呐喊声
2025-06-10 21:42:46 | SUCCESS  | backend.animation.professional_nlu:process_natural_language:426 - Successfully processed 1 actions
2025-06-10 21:42:46 | INFO     | backend.animation.professional_pipeline:process_animation_request:104 - Step 2: Animator Function Processing
2025-06-10 21:42:46 | INFO     | backend.animation.professional_pipeline:_apply_animator_functions:179 - Applying unified animator functions with enhanced features
2025-06-10 21:42:46 | INFO     | backend.animation.animator_functions:create_idle_animation:164 - Creating idle animation: stand
2025-06-10 21:42:46 | INFO     | backend.animation.professional_pipeline:_apply_animator_functions:190 - Optimizing action transitions
2025-06-10 21:42:46 | INFO     | backend.animation.transition_optimizer:optimize_action_sequence:146 - Optimizing sequence of 1 actions...
2025-06-10 21:42:46 | INFO     | backend.animation.professional_pipeline:process_animation_request:110 - Step 3: Generate Blender Animation Data
2025-06-10 21:42:46 | INFO     | backend.animation.professional_pipeline:process_animation_request:114 - Step 4: Execute Blender Animation Generation
2025-06-10 21:42:46 | INFO     | backend.animation.professional_pipeline:_execute_blender_generation:373 - Executing Blender command: /Applications/Blender.app/Contents/MacOS/Blender --background --python blender_scripts/generate_animation.py -- --input temp/animation_data/animation_data_1749562966.json --output output/animations/animation_default_1749562966.fbx --format fbx
2025-06-10 21:42:47 | SUCCESS  | backend.animation.professional_pipeline:_execute_blender_generation:385 - Blender animation generated: output/animations/animation_default_1749562966.fbx
2025-06-10 21:42:47 | WARNING  | backend.animation.professional_pipeline:process_animation_request:119 - Blender generation failed, creating test FBX file
2025-06-10 21:42:47 | INFO     | backend.animation.professional_pipeline:_create_test_fbx_file:570 - Created test FBX file: output/animations/animation_1749562967.fbx
2025-06-10 21:42:47 | INFO     | backend.animation.professional_pipeline:process_animation_request:125 - Step 5: FBX File Validation
2025-06-10 21:42:47 | INFO     | backend.animation.fbx_validator:validate_fbx_file:26 - Starting FBX validation for: output/animations/animation_1749562967.fbx
2025-06-10 21:42:47 | SUCCESS  | backend.animation.fbx_validator:validate_fbx_file:85 - FBX validation completed for: output/animations/animation_1749562967.fbx
2025-06-10 21:42:47 | INFO     | backend.animation.professional_pipeline:process_animation_request:134 - Step 6: Animation Quality Check
2025-06-10 21:42:47 | INFO     | backend.animation.quality_checker:check_animation_quality:130 - Starting animation quality check...
2025-06-10 21:42:47 | SUCCESS  | backend.animation.quality_checker:check_animation_quality:171 - Quality check completed. Overall score: 0.67
2025-06-10 21:42:47 | SUCCESS  | backend.animation.professional_pipeline:process_animation_request:159 - Animation generated successfully in 0.87s
2025-06-10 21:42:47 | INFO     | backend.services.task_service:update_task_status:262 - Updated task d86f4cfc-1b0d-4c3c-88b3-23ec27c4ca27 status to completed
2025-06-10 21:42:47 | INFO     | backend.routers.animation:generate_professional_animation:205 - Updated task d86f4cfc-1b0d-4c3c-88b3-23ec27c4ca27 status to completed
2025-06-10 21:42:47 | SUCCESS  | backend.routers.animation:generate_professional_animation:209 - Professional animation generation completed
2025-06-10 21:48:05 | SUCCESS  | backend.config:validate_config:325 - Configuration validation passed
2025-06-10 21:48:05 | INFO     | backend.app:startup_event:110 - Starting Motion Agent API server with Taskiq...
2025-06-10 21:48:05 | INFO     | backend.app:startup_event:113 - Initializing MongoDB database...
2025-06-10 21:48:05 | INFO     | backend.database:init_database:50 - Initializing MongoDB database...
2025-06-10 21:48:05 | SUCCESS  | backend.database:init_database:81 - MongoDB database initialized successfully: motion_agent
2025-06-10 21:48:05 | INFO     | backend.app:startup_event:120 - Creating database indexes...
2025-06-10 21:48:05 | INFO     | backend.database:create_indexes:259 - Creating database indexes...
2025-06-10 21:48:05 | SUCCESS  | backend.database:create_indexes:279 - Database indexes created successfully
2025-06-10 21:48:05 | INFO     | backend.app:startup_event:124 - Initializing taskiq...
2025-06-10 21:48:05 | INFO     | backend.tasks:init_taskiq:89 - Initializing taskiq...
2025-06-10 21:48:05 | SUCCESS  | backend.tasks:init_taskiq:93 - Taskiq initialized successfully
2025-06-10 21:48:05 | SUCCESS  | backend.app:startup_event:134 - Motion Agent API with Taskiq initialized successfully
2025-06-10 21:48:05 | INFO     | backend.app:startup_event:135 - Motion Agent API is ready to serve requests
2025-06-10 21:48:08 | INFO     | backend.routers.animation:generate_professional_animation:108 - Received professional animation request: 生成一个 关羽登场 的场景，要求如下：
1. 全景，推镜，黄沙漫天，镜头缓缓推进女主，女主在画面中艰难前行，四周响起从快到慢的战鼓声，顺着声音镜头上摇，遮天蔽日的黄沙逐渐散去，时长 8s,声音 风沙声，战鼓声
2. 全景，缓推镜，风沙逐渐散开后出现伟岸的关羽骑士的轮廓，战鼓声逐渐加强。也出现士兵的呐喊声。时长2时。声音战鼓声，呐喊声
2025-06-10 21:48:08 | INFO     | backend.services.task_service:create_task:69 - Created task: 4808f853-1f99-4f09-8c6f-7ed159a96c69 (animation_generation)
2025-06-10 21:48:08 | INFO     | backend.services.task_service:create_animation_task:223 - Created animation task: 4808f853-1f99-4f09-8c6f-7ed159a96c69
2025-06-10 21:48:08 | INFO     | backend.routers.animation:generate_professional_animation:127 - Created animation task: 4808f853-1f99-4f09-8c6f-7ed159a96c69
2025-06-10 21:48:08 | INFO     | backend.services.task_service:update_task_status:262 - Updated task 4808f853-1f99-4f09-8c6f-7ed159a96c69 status to running
2025-06-10 21:48:09 | WARNING  | backend.animation.professional_nlu:_load_spacy_model:55 - No spaCy model found, using blank model
2025-06-10 21:48:09 | INFO     | backend.animation.professional_nlu:_load_sentiment_model:64 - Using rule-based sentiment analysis instead of large model
2025-06-10 21:48:09 | INFO     | backend.animation.professional_nlu:_load_action_classifier:74 - Using rule-based action classification instead of large model
2025-06-10 21:48:09 | INFO     | backend.animation.professional_nlu:__init__:41 - Professional Animator NLU initialized
2025-06-10 21:48:09 | INFO     | backend.animation.animator_functions:__init__:37 - Unified Professional Animator initialized
2025-06-10 21:48:09 | INFO     | backend.animation.professional_pipeline:__init__:84 - Professional Animation Pipeline initialized with enhanced features
2025-06-10 21:48:09 | WARNING  | backend.animation.professional_nlu:_load_spacy_model:55 - No spaCy model found, using blank model
2025-06-10 21:48:09 | INFO     | backend.animation.professional_nlu:_load_sentiment_model:64 - Using rule-based sentiment analysis instead of large model
2025-06-10 21:48:09 | INFO     | backend.animation.professional_nlu:_load_action_classifier:74 - Using rule-based action classification instead of large model
2025-06-10 21:48:09 | INFO     | backend.animation.professional_nlu:__init__:41 - Professional Animator NLU initialized
2025-06-10 21:48:09 | INFO     | backend.animation.animator_functions:__init__:37 - Unified Professional Animator initialized
2025-06-10 21:48:09 | INFO     | backend.animation.professional_pipeline:__init__:84 - Professional Animation Pipeline initialized with enhanced features
2025-06-10 21:48:09 | INFO     | backend.animation.professional_pipeline:process_animation_request:93 - Processing animation request: 生成一个 关羽登场 的场景，要求如下：
1. 全景，推镜，黄沙漫天，镜头缓缓推进女主，女主在画面中艰难前行，四周响起从快到慢的战鼓声，顺着声音镜头上摇，遮天蔽日的黄沙逐渐散去，时长 8s,声音 风沙声，战鼓声
2. 全景，缓推镜，风沙逐渐散开后出现伟岸的关羽骑士的轮廓，战鼓声逐渐加强。也出现士兵的呐喊声。时长2时。声音战鼓声，呐喊声
2025-06-10 21:48:09 | INFO     | backend.animation.professional_pipeline:process_animation_request:97 - Step 1: Natural Language Understanding
2025-06-10 21:48:09 | INFO     | backend.animation.professional_nlu:process_natural_language:397 - Processing animation request: 生成一个 关羽登场 的场景，要求如下：
1. 全景，推镜，黄沙漫天，镜头缓缓推进女主，女主在画面中艰难前行，四周响起从快到慢的战鼓声，顺着声音镜头上摇，遮天蔽日的黄沙逐渐散去，时长 8s,声音 风沙声，战鼓声
2. 全景，缓推镜，风沙逐渐散开后出现伟岸的关羽骑士的轮廓，战鼓声逐渐加强。也出现士兵的呐喊声。时长2时。声音战鼓声，呐喊声
2025-06-10 21:48:09 | SUCCESS  | backend.animation.professional_nlu:process_natural_language:426 - Successfully processed 1 actions
2025-06-10 21:48:09 | INFO     | backend.animation.professional_pipeline:process_animation_request:104 - Step 2: Animator Function Processing
2025-06-10 21:48:09 | INFO     | backend.animation.professional_pipeline:_apply_animator_functions:179 - Applying unified animator functions with enhanced features
2025-06-10 21:48:09 | INFO     | backend.animation.animator_functions:create_idle_animation:164 - Creating idle animation: stand
2025-06-10 21:48:09 | INFO     | backend.animation.professional_pipeline:_apply_animator_functions:190 - Optimizing action transitions
2025-06-10 21:48:09 | INFO     | backend.animation.transition_optimizer:optimize_action_sequence:146 - Optimizing sequence of 1 actions...
2025-06-10 21:48:09 | INFO     | backend.animation.professional_pipeline:process_animation_request:110 - Step 3: Generate Blender Animation Data
2025-06-10 21:48:09 | INFO     | backend.animation.professional_pipeline:process_animation_request:114 - Step 4: Execute Blender Animation Generation
2025-06-10 21:48:09 | INFO     | backend.animation.professional_pipeline:_execute_blender_generation:373 - Executing Blender command: /Applications/Blender.app/Contents/MacOS/Blender --background --python blender_scripts/generate_animation.py -- --input temp/animation_data/animation_data_1749563289.json --output output/animations/animation_default_1749563289.fbx --format fbx
2025-06-10 21:48:10 | SUCCESS  | backend.animation.professional_pipeline:_execute_blender_generation:385 - Blender animation generated: output/animations/animation_default_1749563289.fbx
2025-06-10 21:48:10 | WARNING  | backend.animation.professional_pipeline:process_animation_request:119 - Blender generation failed, creating test FBX file
2025-06-10 21:48:10 | INFO     | backend.animation.professional_pipeline:_create_test_fbx_file:570 - Created test FBX file: output/animations/animation_1749563290.fbx
2025-06-10 21:48:10 | INFO     | backend.animation.professional_pipeline:process_animation_request:125 - Step 5: FBX File Validation
2025-06-10 21:48:10 | INFO     | backend.animation.fbx_validator:validate_fbx_file:26 - Starting FBX validation for: output/animations/animation_1749563290.fbx
2025-06-10 21:48:10 | SUCCESS  | backend.animation.fbx_validator:validate_fbx_file:85 - FBX validation completed for: output/animations/animation_1749563290.fbx
2025-06-10 21:48:10 | INFO     | backend.animation.professional_pipeline:process_animation_request:134 - Step 6: Animation Quality Check
2025-06-10 21:48:10 | INFO     | backend.animation.quality_checker:check_animation_quality:130 - Starting animation quality check...
2025-06-10 21:48:10 | SUCCESS  | backend.animation.quality_checker:check_animation_quality:171 - Quality check completed. Overall score: 0.67
2025-06-10 21:48:10 | SUCCESS  | backend.animation.professional_pipeline:process_animation_request:159 - Animation generated successfully in 0.76s
2025-06-10 21:48:10 | INFO     | backend.services.task_service:update_task_status:262 - Updated task 4808f853-1f99-4f09-8c6f-7ed159a96c69 status to completed
2025-06-10 21:48:10 | INFO     | backend.routers.animation:generate_professional_animation:205 - Updated task 4808f853-1f99-4f09-8c6f-7ed159a96c69 status to completed
2025-06-10 21:48:10 | SUCCESS  | backend.routers.animation:generate_professional_animation:209 - Professional animation generation completed
2025-06-10 21:48:13 | INFO     | backend.routers.animation:download_animation_file:249 - Download request for file: animation_1749563290.fbx
2025-06-10 21:48:13 | INFO     | backend.routers.animation:download_animation_file:264 - Serving file: animation_1749563290.fbx (1212 bytes)
2025-06-10 21:48:29 | INFO     | backend.routers.tasks:list_tasks:38 - Listed 9 tasks
2025-06-10 21:48:29 | INFO     | backend.routers.tasks:list_tasks:38 - Listed 9 tasks
2025-06-10 21:48:36 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 21:49:07 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 21:49:37 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 21:50:07 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 21:50:37 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 21:51:07 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 21:51:37 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 21:52:08 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 21:52:18 | INFO     | backend.routers.animation:generate_professional_animation:108 - Received professional animation request: 后空翻, 拳击, walk, 步行, soccer
2025-06-10 21:52:18 | INFO     | backend.services.task_service:create_task:69 - Created task: cb475c4d-c5ed-4924-bb80-19853204d00f (animation_generation)
2025-06-10 21:52:18 | INFO     | backend.services.task_service:create_animation_task:223 - Created animation task: cb475c4d-c5ed-4924-bb80-19853204d00f
2025-06-10 21:52:18 | INFO     | backend.routers.animation:generate_professional_animation:127 - Created animation task: cb475c4d-c5ed-4924-bb80-19853204d00f
2025-06-10 21:52:18 | INFO     | backend.services.task_service:update_task_status:262 - Updated task cb475c4d-c5ed-4924-bb80-19853204d00f status to running
2025-06-10 21:52:18 | WARNING  | backend.animation.professional_nlu:_load_spacy_model:55 - No spaCy model found, using blank model
2025-06-10 21:52:18 | INFO     | backend.animation.professional_nlu:_load_sentiment_model:64 - Using rule-based sentiment analysis instead of large model
2025-06-10 21:52:18 | INFO     | backend.animation.professional_nlu:_load_action_classifier:74 - Using rule-based action classification instead of large model
2025-06-10 21:52:18 | INFO     | backend.animation.professional_nlu:__init__:41 - Professional Animator NLU initialized
2025-06-10 21:52:18 | INFO     | backend.animation.animator_functions:__init__:37 - Unified Professional Animator initialized
2025-06-10 21:52:18 | INFO     | backend.animation.professional_pipeline:__init__:84 - Professional Animation Pipeline initialized with enhanced features
2025-06-10 21:52:18 | INFO     | backend.animation.professional_pipeline:process_animation_request:93 - Processing animation request: 后空翻, 拳击, walk, 步行, soccer
2025-06-10 21:52:18 | INFO     | backend.animation.professional_pipeline:process_animation_request:97 - Step 1: Natural Language Understanding
2025-06-10 21:52:18 | INFO     | backend.animation.professional_nlu:process_natural_language:397 - Processing animation request: 后空翻, 拳击, walk, 步行, soccer
2025-06-10 21:52:18 | SUCCESS  | backend.animation.professional_nlu:process_natural_language:426 - Successfully processed 1 actions
2025-06-10 21:52:18 | INFO     | backend.animation.professional_pipeline:process_animation_request:104 - Step 2: Animator Function Processing
2025-06-10 21:52:18 | INFO     | backend.animation.professional_pipeline:_apply_animator_functions:179 - Applying unified animator functions with enhanced features
2025-06-10 21:52:18 | INFO     | backend.animation.animator_functions:create_walk_cycle:66 - Creating walk cycle: None, AnimationIntensity.NORMAL, steps=0
2025-06-10 21:52:18 | INFO     | backend.animation.professional_pipeline:_apply_animator_functions:190 - Optimizing action transitions
2025-06-10 21:52:18 | INFO     | backend.animation.transition_optimizer:optimize_action_sequence:146 - Optimizing sequence of 1 actions...
2025-06-10 21:52:18 | INFO     | backend.animation.professional_pipeline:process_animation_request:110 - Step 3: Generate Blender Animation Data
2025-06-10 21:52:18 | INFO     | backend.animation.professional_pipeline:process_animation_request:114 - Step 4: Execute Blender Animation Generation
2025-06-10 21:52:18 | INFO     | backend.animation.professional_pipeline:_execute_blender_generation:373 - Executing Blender command: /Applications/Blender.app/Contents/MacOS/Blender --background --python blender_scripts/generate_animation.py -- --input temp/animation_data/animation_data_1749563538.json --output output/animations/animation_default_1749563538.fbx --format fbx
2025-06-10 21:52:19 | SUCCESS  | backend.animation.professional_pipeline:_execute_blender_generation:385 - Blender animation generated: output/animations/animation_default_1749563538.fbx
2025-06-10 21:52:19 | WARNING  | backend.animation.professional_pipeline:process_animation_request:119 - Blender generation failed, creating test FBX file
2025-06-10 21:52:19 | INFO     | backend.animation.professional_pipeline:_create_test_fbx_file:570 - Created test FBX file: output/animations/animation_1749563539.fbx
2025-06-10 21:52:19 | INFO     | backend.animation.professional_pipeline:process_animation_request:125 - Step 5: FBX File Validation
2025-06-10 21:52:19 | INFO     | backend.animation.fbx_validator:validate_fbx_file:26 - Starting FBX validation for: output/animations/animation_1749563539.fbx
2025-06-10 21:52:19 | SUCCESS  | backend.animation.fbx_validator:validate_fbx_file:85 - FBX validation completed for: output/animations/animation_1749563539.fbx
2025-06-10 21:52:19 | INFO     | backend.animation.professional_pipeline:process_animation_request:134 - Step 6: Animation Quality Check
2025-06-10 21:52:19 | INFO     | backend.animation.quality_checker:check_animation_quality:130 - Starting animation quality check...
2025-06-10 21:52:19 | SUCCESS  | backend.animation.quality_checker:check_animation_quality:171 - Quality check completed. Overall score: 0.67
2025-06-10 21:52:19 | SUCCESS  | backend.animation.professional_pipeline:process_animation_request:159 - Animation generated successfully in 0.87s
2025-06-10 21:52:19 | INFO     | backend.services.task_service:update_task_status:262 - Updated task cb475c4d-c5ed-4924-bb80-19853204d00f status to completed
2025-06-10 21:52:19 | INFO     | backend.routers.animation:generate_professional_animation:205 - Updated task cb475c4d-c5ed-4924-bb80-19853204d00f status to completed
2025-06-10 21:52:19 | SUCCESS  | backend.routers.animation:generate_professional_animation:209 - Professional animation generation completed
2025-06-10 21:52:21 | INFO     | backend.routers.animation:download_animation_file:249 - Download request for file: animation_1749563539.fbx
2025-06-10 21:52:21 | INFO     | backend.routers.animation:download_animation_file:264 - Serving file: animation_1749563539.fbx (1218 bytes)
2025-06-10 21:52:36 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 21:53:07 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 21:53:37 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 21:54:24 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 21:54:36 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 21:55:06 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 21:55:36 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 21:56:07 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 21:56:37 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 21:57:25 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 21:58:25 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 21:59:25 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 22:27:33 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 22:28:03 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 22:28:33 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 22:29:03 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 22:29:33 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 22:30:03 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 22:32:29 | SUCCESS  | backend.config:validate_config:325 - Configuration validation passed
2025-06-10 22:32:29 | INFO     | backend.app:startup_event:110 - Starting Motion Agent API server with Taskiq...
2025-06-10 22:32:29 | INFO     | backend.app:startup_event:113 - Initializing MongoDB database...
2025-06-10 22:32:29 | INFO     | backend.database:init_database:50 - Initializing MongoDB database...
2025-06-10 22:32:29 | SUCCESS  | backend.database:init_database:81 - MongoDB database initialized successfully: motion_agent
2025-06-10 22:32:29 | INFO     | backend.app:startup_event:120 - Creating database indexes...
2025-06-10 22:32:29 | INFO     | backend.database:create_indexes:259 - Creating database indexes...
2025-06-10 22:32:29 | SUCCESS  | backend.database:create_indexes:279 - Database indexes created successfully
2025-06-10 22:32:29 | INFO     | backend.app:startup_event:124 - Initializing taskiq...
2025-06-10 22:32:29 | INFO     | backend.tasks:init_taskiq:89 - Initializing taskiq...
2025-06-10 22:32:29 | SUCCESS  | backend.tasks:init_taskiq:93 - Taskiq initialized successfully
2025-06-10 22:32:29 | SUCCESS  | backend.app:startup_event:134 - Motion Agent API with Taskiq initialized successfully
2025-06-10 22:32:29 | INFO     | backend.app:startup_event:135 - Motion Agent API is ready to serve requests
2025-06-10 22:32:52 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 22:33:41 | SUCCESS  | backend.config:validate_config:325 - Configuration validation passed
2025-06-10 22:33:41 | INFO     | backend.app:startup_event:110 - Starting Motion Agent API server with Taskiq...
2025-06-10 22:33:41 | INFO     | backend.app:startup_event:113 - Initializing MongoDB database...
2025-06-10 22:33:41 | INFO     | backend.database:init_database:50 - Initializing MongoDB database...
2025-06-10 22:33:41 | SUCCESS  | backend.database:init_database:81 - MongoDB database initialized successfully: motion_agent
2025-06-10 22:33:41 | INFO     | backend.app:startup_event:120 - Creating database indexes...
2025-06-10 22:33:41 | INFO     | backend.database:create_indexes:259 - Creating database indexes...
2025-06-10 22:33:41 | SUCCESS  | backend.database:create_indexes:279 - Database indexes created successfully
2025-06-10 22:33:41 | INFO     | backend.app:startup_event:124 - Initializing taskiq...
2025-06-10 22:33:41 | INFO     | backend.tasks:init_taskiq:89 - Initializing taskiq...
2025-06-10 22:33:41 | SUCCESS  | backend.tasks:init_taskiq:93 - Taskiq initialized successfully
2025-06-10 22:33:41 | SUCCESS  | backend.app:startup_event:134 - Motion Agent API with Taskiq initialized successfully
2025-06-10 22:33:41 | INFO     | backend.app:startup_event:135 - Motion Agent API is ready to serve requests
2025-06-10 22:33:52 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 22:34:22 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 22:34:24 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 22:34:52 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 22:35:05 | INFO     | backend.app:shutdown_event:141 - Shutting down Motion Agent API...
2025-06-10 22:35:05 | INFO     | backend.tasks:shutdown_taskiq:104 - Shutting down taskiq...
2025-06-10 22:35:05 | SUCCESS  | backend.tasks:shutdown_taskiq:106 - Taskiq shutdown successfully
2025-06-10 22:35:05 | INFO     | backend.database:close_database:94 - Closing MongoDB connections...
2025-06-10 22:35:05 | SUCCESS  | backend.database:close_database:101 - MongoDB connections closed
2025-06-10 22:35:05 | SUCCESS  | backend.app:shutdown_event:149 - Motion Agent API shutdown completed
2025-06-10 22:35:05 | SUCCESS  | backend.config:validate_config:325 - Configuration validation passed
2025-06-10 22:35:05 | INFO     | backend.app:startup_event:110 - Starting Motion Agent API server with Taskiq...
2025-06-10 22:35:05 | INFO     | backend.app:startup_event:113 - Initializing MongoDB database...
2025-06-10 22:35:05 | INFO     | backend.database:init_database:50 - Initializing MongoDB database...
2025-06-10 22:35:05 | SUCCESS  | backend.database:init_database:81 - MongoDB database initialized successfully: motion_agent
2025-06-10 22:35:05 | INFO     | backend.app:startup_event:120 - Creating database indexes...
2025-06-10 22:35:05 | INFO     | backend.database:create_indexes:259 - Creating database indexes...
2025-06-10 22:35:05 | SUCCESS  | backend.database:create_indexes:279 - Database indexes created successfully
2025-06-10 22:35:05 | INFO     | backend.app:startup_event:124 - Initializing taskiq...
2025-06-10 22:35:05 | INFO     | backend.tasks:init_taskiq:89 - Initializing taskiq...
2025-06-10 22:35:05 | SUCCESS  | backend.tasks:init_taskiq:93 - Taskiq initialized successfully
2025-06-10 22:35:05 | SUCCESS  | backend.app:startup_event:134 - Motion Agent API with Taskiq initialized successfully
2025-06-10 22:35:05 | INFO     | backend.app:startup_event:135 - Motion Agent API is ready to serve requests
2025-06-10 22:35:22 | INFO     | backend.app:shutdown_event:141 - Shutting down Motion Agent API...
2025-06-10 22:35:22 | INFO     | backend.tasks:shutdown_taskiq:104 - Shutting down taskiq...
2025-06-10 22:35:22 | SUCCESS  | backend.tasks:shutdown_taskiq:106 - Taskiq shutdown successfully
2025-06-10 22:35:22 | INFO     | backend.database:close_database:94 - Closing MongoDB connections...
2025-06-10 22:35:22 | SUCCESS  | backend.database:close_database:101 - MongoDB connections closed
2025-06-10 22:35:22 | SUCCESS  | backend.app:shutdown_event:149 - Motion Agent API shutdown completed
2025-06-10 22:35:22 | SUCCESS  | backend.config:validate_config:325 - Configuration validation passed
2025-06-10 22:35:22 | INFO     | backend.app:startup_event:110 - Starting Motion Agent API server with Taskiq...
2025-06-10 22:35:22 | INFO     | backend.app:startup_event:113 - Initializing MongoDB database...
2025-06-10 22:35:22 | INFO     | backend.database:init_database:50 - Initializing MongoDB database...
2025-06-10 22:35:22 | SUCCESS  | backend.database:init_database:81 - MongoDB database initialized successfully: motion_agent
2025-06-10 22:35:22 | INFO     | backend.app:startup_event:120 - Creating database indexes...
2025-06-10 22:35:22 | INFO     | backend.database:create_indexes:259 - Creating database indexes...
2025-06-10 22:35:22 | SUCCESS  | backend.database:create_indexes:279 - Database indexes created successfully
2025-06-10 22:35:22 | INFO     | backend.app:startup_event:124 - Initializing taskiq...
2025-06-10 22:35:22 | INFO     | backend.tasks:init_taskiq:89 - Initializing taskiq...
2025-06-10 22:35:22 | SUCCESS  | backend.tasks:init_taskiq:93 - Taskiq initialized successfully
2025-06-10 22:35:22 | SUCCESS  | backend.app:startup_event:134 - Motion Agent API with Taskiq initialized successfully
2025-06-10 22:35:22 | INFO     | backend.app:startup_event:135 - Motion Agent API is ready to serve requests
2025-06-10 22:35:24 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 22:35:24 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 22:35:36 | INFO     | backend.app:shutdown_event:141 - Shutting down Motion Agent API...
2025-06-10 22:35:36 | INFO     | backend.tasks:shutdown_taskiq:104 - Shutting down taskiq...
2025-06-10 22:35:36 | SUCCESS  | backend.tasks:shutdown_taskiq:106 - Taskiq shutdown successfully
2025-06-10 22:35:36 | INFO     | backend.database:close_database:94 - Closing MongoDB connections...
2025-06-10 22:35:36 | SUCCESS  | backend.database:close_database:101 - MongoDB connections closed
2025-06-10 22:35:36 | SUCCESS  | backend.app:shutdown_event:149 - Motion Agent API shutdown completed
2025-06-10 22:35:37 | SUCCESS  | backend.config:validate_config:325 - Configuration validation passed
2025-06-10 22:35:37 | INFO     | backend.app:startup_event:110 - Starting Motion Agent API server with Taskiq...
2025-06-10 22:35:37 | INFO     | backend.app:startup_event:113 - Initializing MongoDB database...
2025-06-10 22:35:37 | INFO     | backend.database:init_database:50 - Initializing MongoDB database...
2025-06-10 22:35:37 | SUCCESS  | backend.database:init_database:81 - MongoDB database initialized successfully: motion_agent
2025-06-10 22:35:37 | INFO     | backend.app:startup_event:120 - Creating database indexes...
2025-06-10 22:35:37 | INFO     | backend.database:create_indexes:259 - Creating database indexes...
2025-06-10 22:35:37 | SUCCESS  | backend.database:create_indexes:279 - Database indexes created successfully
2025-06-10 22:35:37 | INFO     | backend.app:startup_event:124 - Initializing taskiq...
2025-06-10 22:35:37 | INFO     | backend.tasks:init_taskiq:89 - Initializing taskiq...
2025-06-10 22:35:37 | SUCCESS  | backend.tasks:init_taskiq:93 - Taskiq initialized successfully
2025-06-10 22:35:37 | SUCCESS  | backend.app:startup_event:134 - Motion Agent API with Taskiq initialized successfully
2025-06-10 22:35:37 | INFO     | backend.app:startup_event:135 - Motion Agent API is ready to serve requests
2025-06-10 22:35:54 | INFO     | backend.app:shutdown_event:141 - Shutting down Motion Agent API...
2025-06-10 22:35:54 | INFO     | backend.tasks:shutdown_taskiq:104 - Shutting down taskiq...
2025-06-10 22:35:54 | SUCCESS  | backend.tasks:shutdown_taskiq:106 - Taskiq shutdown successfully
2025-06-10 22:35:54 | INFO     | backend.database:close_database:94 - Closing MongoDB connections...
2025-06-10 22:35:54 | SUCCESS  | backend.database:close_database:101 - MongoDB connections closed
2025-06-10 22:35:54 | SUCCESS  | backend.app:shutdown_event:149 - Motion Agent API shutdown completed
2025-06-10 22:35:55 | SUCCESS  | backend.config:validate_config:325 - Configuration validation passed
2025-06-10 22:35:55 | INFO     | backend.app:startup_event:110 - Starting Motion Agent API server with Taskiq...
2025-06-10 22:35:55 | INFO     | backend.app:startup_event:113 - Initializing MongoDB database...
2025-06-10 22:35:55 | INFO     | backend.database:init_database:50 - Initializing MongoDB database...
2025-06-10 22:35:55 | SUCCESS  | backend.database:init_database:81 - MongoDB database initialized successfully: motion_agent
2025-06-10 22:35:55 | INFO     | backend.app:startup_event:120 - Creating database indexes...
2025-06-10 22:35:55 | INFO     | backend.database:create_indexes:259 - Creating database indexes...
2025-06-10 22:35:55 | SUCCESS  | backend.database:create_indexes:279 - Database indexes created successfully
2025-06-10 22:35:55 | INFO     | backend.app:startup_event:124 - Initializing taskiq...
2025-06-10 22:35:55 | INFO     | backend.tasks:init_taskiq:89 - Initializing taskiq...
2025-06-10 22:35:55 | SUCCESS  | backend.tasks:init_taskiq:93 - Taskiq initialized successfully
2025-06-10 22:35:55 | SUCCESS  | backend.app:startup_event:134 - Motion Agent API with Taskiq initialized successfully
2025-06-10 22:35:55 | INFO     | backend.app:startup_event:135 - Motion Agent API is ready to serve requests
2025-06-10 22:35:59 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 22:36:08 | INFO     | backend.app:shutdown_event:141 - Shutting down Motion Agent API...
2025-06-10 22:36:08 | INFO     | backend.tasks:shutdown_taskiq:104 - Shutting down taskiq...
2025-06-10 22:36:08 | SUCCESS  | backend.tasks:shutdown_taskiq:106 - Taskiq shutdown successfully
2025-06-10 22:36:08 | INFO     | backend.database:close_database:94 - Closing MongoDB connections...
2025-06-10 22:36:08 | SUCCESS  | backend.database:close_database:101 - MongoDB connections closed
2025-06-10 22:36:08 | SUCCESS  | backend.app:shutdown_event:149 - Motion Agent API shutdown completed
2025-06-10 22:36:08 | SUCCESS  | backend.config:validate_config:325 - Configuration validation passed
2025-06-10 22:36:08 | INFO     | backend.app:startup_event:110 - Starting Motion Agent API server with Taskiq...
2025-06-10 22:36:08 | INFO     | backend.app:startup_event:113 - Initializing MongoDB database...
2025-06-10 22:36:08 | INFO     | backend.database:init_database:50 - Initializing MongoDB database...
2025-06-10 22:36:08 | SUCCESS  | backend.database:init_database:81 - MongoDB database initialized successfully: motion_agent
2025-06-10 22:36:08 | INFO     | backend.app:startup_event:120 - Creating database indexes...
2025-06-10 22:36:08 | INFO     | backend.database:create_indexes:259 - Creating database indexes...
2025-06-10 22:36:08 | SUCCESS  | backend.database:create_indexes:279 - Database indexes created successfully
2025-06-10 22:36:08 | INFO     | backend.app:startup_event:124 - Initializing taskiq...
2025-06-10 22:36:08 | INFO     | backend.tasks:init_taskiq:89 - Initializing taskiq...
2025-06-10 22:36:08 | SUCCESS  | backend.tasks:init_taskiq:93 - Taskiq initialized successfully
2025-06-10 22:36:08 | SUCCESS  | backend.app:startup_event:134 - Motion Agent API with Taskiq initialized successfully
2025-06-10 22:36:08 | INFO     | backend.app:startup_event:135 - Motion Agent API is ready to serve requests
2025-06-10 22:36:22 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 22:36:24 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 22:36:36 | INFO     | backend.app:shutdown_event:141 - Shutting down Motion Agent API...
2025-06-10 22:36:36 | INFO     | backend.tasks:shutdown_taskiq:104 - Shutting down taskiq...
2025-06-10 22:36:36 | SUCCESS  | backend.tasks:shutdown_taskiq:106 - Taskiq shutdown successfully
2025-06-10 22:36:36 | INFO     | backend.database:close_database:94 - Closing MongoDB connections...
2025-06-10 22:36:36 | SUCCESS  | backend.database:close_database:101 - MongoDB connections closed
2025-06-10 22:36:36 | SUCCESS  | backend.app:shutdown_event:149 - Motion Agent API shutdown completed
2025-06-10 22:36:36 | SUCCESS  | backend.config:validate_config:325 - Configuration validation passed
2025-06-10 22:36:36 | INFO     | backend.app:startup_event:110 - Starting Motion Agent API server with Taskiq...
2025-06-10 22:36:36 | INFO     | backend.app:startup_event:113 - Initializing MongoDB database...
2025-06-10 22:36:36 | INFO     | backend.database:init_database:50 - Initializing MongoDB database...
2025-06-10 22:36:36 | SUCCESS  | backend.database:init_database:81 - MongoDB database initialized successfully: motion_agent
2025-06-10 22:36:36 | INFO     | backend.app:startup_event:120 - Creating database indexes...
2025-06-10 22:36:36 | INFO     | backend.database:create_indexes:259 - Creating database indexes...
2025-06-10 22:36:36 | SUCCESS  | backend.database:create_indexes:279 - Database indexes created successfully
2025-06-10 22:36:36 | INFO     | backend.app:startup_event:124 - Initializing taskiq...
2025-06-10 22:36:36 | INFO     | backend.tasks:init_taskiq:89 - Initializing taskiq...
2025-06-10 22:36:36 | SUCCESS  | backend.tasks:init_taskiq:93 - Taskiq initialized successfully
2025-06-10 22:36:36 | SUCCESS  | backend.app:startup_event:134 - Motion Agent API with Taskiq initialized successfully
2025-06-10 22:36:36 | INFO     | backend.app:startup_event:135 - Motion Agent API is ready to serve requests
2025-06-10 22:36:52 | INFO     | backend.app:shutdown_event:141 - Shutting down Motion Agent API...
2025-06-10 22:36:52 | INFO     | backend.tasks:shutdown_taskiq:104 - Shutting down taskiq...
2025-06-10 22:36:52 | SUCCESS  | backend.tasks:shutdown_taskiq:106 - Taskiq shutdown successfully
2025-06-10 22:36:52 | INFO     | backend.database:close_database:94 - Closing MongoDB connections...
2025-06-10 22:36:52 | SUCCESS  | backend.database:close_database:101 - MongoDB connections closed
2025-06-10 22:36:52 | SUCCESS  | backend.app:shutdown_event:149 - Motion Agent API shutdown completed
2025-06-10 22:36:52 | SUCCESS  | backend.config:validate_config:325 - Configuration validation passed
2025-06-10 22:36:52 | INFO     | backend.app:startup_event:110 - Starting Motion Agent API server with Taskiq...
2025-06-10 22:36:52 | INFO     | backend.app:startup_event:113 - Initializing MongoDB database...
2025-06-10 22:36:52 | INFO     | backend.database:init_database:50 - Initializing MongoDB database...
2025-06-10 22:36:52 | SUCCESS  | backend.database:init_database:81 - MongoDB database initialized successfully: motion_agent
2025-06-10 22:36:52 | INFO     | backend.app:startup_event:120 - Creating database indexes...
2025-06-10 22:36:52 | INFO     | backend.database:create_indexes:259 - Creating database indexes...
2025-06-10 22:36:52 | SUCCESS  | backend.database:create_indexes:279 - Database indexes created successfully
2025-06-10 22:36:52 | INFO     | backend.app:startup_event:124 - Initializing taskiq...
2025-06-10 22:36:52 | INFO     | backend.tasks:init_taskiq:89 - Initializing taskiq...
2025-06-10 22:36:52 | SUCCESS  | backend.tasks:init_taskiq:93 - Taskiq initialized successfully
2025-06-10 22:36:52 | SUCCESS  | backend.app:startup_event:134 - Motion Agent API with Taskiq initialized successfully
2025-06-10 22:36:52 | INFO     | backend.app:startup_event:135 - Motion Agent API is ready to serve requests
2025-06-10 22:36:52 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 22:37:11 | INFO     | backend.app:shutdown_event:141 - Shutting down Motion Agent API...
2025-06-10 22:37:11 | INFO     | backend.tasks:shutdown_taskiq:104 - Shutting down taskiq...
2025-06-10 22:37:11 | SUCCESS  | backend.tasks:shutdown_taskiq:106 - Taskiq shutdown successfully
2025-06-10 22:37:11 | INFO     | backend.database:close_database:94 - Closing MongoDB connections...
2025-06-10 22:37:11 | SUCCESS  | backend.database:close_database:101 - MongoDB connections closed
2025-06-10 22:37:11 | SUCCESS  | backend.app:shutdown_event:149 - Motion Agent API shutdown completed
2025-06-10 22:37:11 | SUCCESS  | backend.config:validate_config:325 - Configuration validation passed
2025-06-10 22:37:11 | INFO     | backend.app:startup_event:110 - Starting Motion Agent API server with Taskiq...
2025-06-10 22:37:11 | INFO     | backend.app:startup_event:113 - Initializing MongoDB database...
2025-06-10 22:37:11 | INFO     | backend.database:init_database:50 - Initializing MongoDB database...
2025-06-10 22:37:11 | SUCCESS  | backend.database:init_database:81 - MongoDB database initialized successfully: motion_agent
2025-06-10 22:37:11 | INFO     | backend.app:startup_event:120 - Creating database indexes...
2025-06-10 22:37:11 | INFO     | backend.database:create_indexes:259 - Creating database indexes...
2025-06-10 22:37:11 | SUCCESS  | backend.database:create_indexes:279 - Database indexes created successfully
2025-06-10 22:37:11 | INFO     | backend.app:startup_event:124 - Initializing taskiq...
2025-06-10 22:37:11 | INFO     | backend.tasks:init_taskiq:89 - Initializing taskiq...
2025-06-10 22:37:11 | SUCCESS  | backend.tasks:init_taskiq:93 - Taskiq initialized successfully
2025-06-10 22:37:11 | SUCCESS  | backend.app:startup_event:134 - Motion Agent API with Taskiq initialized successfully
2025-06-10 22:37:11 | INFO     | backend.app:startup_event:135 - Motion Agent API is ready to serve requests
2025-06-10 22:37:24 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 22:37:24 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 22:37:31 | INFO     | backend.app:shutdown_event:141 - Shutting down Motion Agent API...
2025-06-10 22:37:31 | INFO     | backend.tasks:shutdown_taskiq:104 - Shutting down taskiq...
2025-06-10 22:37:31 | SUCCESS  | backend.tasks:shutdown_taskiq:106 - Taskiq shutdown successfully
2025-06-10 22:37:31 | INFO     | backend.database:close_database:94 - Closing MongoDB connections...
2025-06-10 22:37:31 | SUCCESS  | backend.database:close_database:101 - MongoDB connections closed
2025-06-10 22:37:31 | SUCCESS  | backend.app:shutdown_event:149 - Motion Agent API shutdown completed
2025-06-10 22:37:31 | SUCCESS  | backend.config:validate_config:325 - Configuration validation passed
2025-06-10 22:37:31 | INFO     | backend.app:startup_event:110 - Starting Motion Agent API server with Taskiq...
2025-06-10 22:37:31 | INFO     | backend.app:startup_event:113 - Initializing MongoDB database...
2025-06-10 22:37:31 | INFO     | backend.database:init_database:50 - Initializing MongoDB database...
2025-06-10 22:37:31 | SUCCESS  | backend.database:init_database:81 - MongoDB database initialized successfully: motion_agent
2025-06-10 22:37:31 | INFO     | backend.app:startup_event:120 - Creating database indexes...
2025-06-10 22:37:31 | INFO     | backend.database:create_indexes:259 - Creating database indexes...
2025-06-10 22:37:31 | SUCCESS  | backend.database:create_indexes:279 - Database indexes created successfully
2025-06-10 22:37:31 | INFO     | backend.app:startup_event:124 - Initializing taskiq...
2025-06-10 22:37:31 | INFO     | backend.tasks:init_taskiq:89 - Initializing taskiq...
2025-06-10 22:37:31 | SUCCESS  | backend.tasks:init_taskiq:93 - Taskiq initialized successfully
2025-06-10 22:37:31 | SUCCESS  | backend.app:startup_event:134 - Motion Agent API with Taskiq initialized successfully
2025-06-10 22:37:31 | INFO     | backend.app:startup_event:135 - Motion Agent API is ready to serve requests
2025-06-10 22:37:55 | INFO     | backend.app:shutdown_event:141 - Shutting down Motion Agent API...
2025-06-10 22:37:55 | INFO     | backend.tasks:shutdown_taskiq:104 - Shutting down taskiq...
2025-06-10 22:37:55 | SUCCESS  | backend.tasks:shutdown_taskiq:106 - Taskiq shutdown successfully
2025-06-10 22:37:55 | INFO     | backend.database:close_database:94 - Closing MongoDB connections...
2025-06-10 22:37:55 | SUCCESS  | backend.database:close_database:101 - MongoDB connections closed
2025-06-10 22:37:55 | SUCCESS  | backend.app:shutdown_event:149 - Motion Agent API shutdown completed
2025-06-10 22:37:55 | SUCCESS  | backend.config:validate_config:325 - Configuration validation passed
2025-06-10 22:37:55 | INFO     | backend.app:startup_event:110 - Starting Motion Agent API server with Taskiq...
2025-06-10 22:37:55 | INFO     | backend.app:startup_event:113 - Initializing MongoDB database...
2025-06-10 22:37:55 | INFO     | backend.database:init_database:50 - Initializing MongoDB database...
2025-06-10 22:37:55 | SUCCESS  | backend.database:init_database:81 - MongoDB database initialized successfully: motion_agent
2025-06-10 22:37:55 | INFO     | backend.app:startup_event:120 - Creating database indexes...
2025-06-10 22:37:55 | INFO     | backend.database:create_indexes:259 - Creating database indexes...
2025-06-10 22:37:55 | SUCCESS  | backend.database:create_indexes:279 - Database indexes created successfully
2025-06-10 22:37:55 | INFO     | backend.app:startup_event:124 - Initializing taskiq...
2025-06-10 22:37:55 | INFO     | backend.tasks:init_taskiq:89 - Initializing taskiq...
2025-06-10 22:37:55 | SUCCESS  | backend.tasks:init_taskiq:93 - Taskiq initialized successfully
2025-06-10 22:37:55 | SUCCESS  | backend.app:startup_event:134 - Motion Agent API with Taskiq initialized successfully
2025-06-10 22:37:55 | INFO     | backend.app:startup_event:135 - Motion Agent API is ready to serve requests
2025-06-10 22:38:24 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 22:38:24 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 22:38:31 | INFO     | backend.app:shutdown_event:141 - Shutting down Motion Agent API...
2025-06-10 22:38:31 | INFO     | backend.tasks:shutdown_taskiq:104 - Shutting down taskiq...
2025-06-10 22:38:31 | SUCCESS  | backend.tasks:shutdown_taskiq:106 - Taskiq shutdown successfully
2025-06-10 22:38:31 | INFO     | backend.database:close_database:94 - Closing MongoDB connections...
2025-06-10 22:38:31 | SUCCESS  | backend.database:close_database:101 - MongoDB connections closed
2025-06-10 22:38:31 | SUCCESS  | backend.app:shutdown_event:149 - Motion Agent API shutdown completed
2025-06-10 22:38:31 | SUCCESS  | backend.config:validate_config:325 - Configuration validation passed
2025-06-10 22:38:31 | INFO     | backend.app:startup_event:110 - Starting Motion Agent API server with Taskiq...
2025-06-10 22:38:31 | INFO     | backend.app:startup_event:113 - Initializing MongoDB database...
2025-06-10 22:38:31 | INFO     | backend.database:init_database:50 - Initializing MongoDB database...
2025-06-10 22:38:31 | SUCCESS  | backend.database:init_database:81 - MongoDB database initialized successfully: motion_agent
2025-06-10 22:38:31 | INFO     | backend.app:startup_event:120 - Creating database indexes...
2025-06-10 22:38:31 | INFO     | backend.database:create_indexes:259 - Creating database indexes...
2025-06-10 22:38:31 | SUCCESS  | backend.database:create_indexes:279 - Database indexes created successfully
2025-06-10 22:38:31 | INFO     | backend.app:startup_event:124 - Initializing taskiq...
2025-06-10 22:38:31 | INFO     | backend.tasks:init_taskiq:89 - Initializing taskiq...
2025-06-10 22:38:31 | SUCCESS  | backend.tasks:init_taskiq:93 - Taskiq initialized successfully
2025-06-10 22:38:31 | SUCCESS  | backend.app:startup_event:134 - Motion Agent API with Taskiq initialized successfully
2025-06-10 22:38:31 | INFO     | backend.app:startup_event:135 - Motion Agent API is ready to serve requests
2025-06-10 22:38:46 | INFO     | backend.app:shutdown_event:141 - Shutting down Motion Agent API...
2025-06-10 22:38:46 | INFO     | backend.tasks:shutdown_taskiq:104 - Shutting down taskiq...
2025-06-10 22:38:46 | SUCCESS  | backend.tasks:shutdown_taskiq:106 - Taskiq shutdown successfully
2025-06-10 22:38:46 | INFO     | backend.database:close_database:94 - Closing MongoDB connections...
2025-06-10 22:38:46 | SUCCESS  | backend.database:close_database:101 - MongoDB connections closed
2025-06-10 22:38:46 | SUCCESS  | backend.app:shutdown_event:149 - Motion Agent API shutdown completed
2025-06-10 22:38:47 | SUCCESS  | backend.config:validate_config:325 - Configuration validation passed
2025-06-10 22:38:47 | INFO     | backend.app:startup_event:110 - Starting Motion Agent API server with Taskiq...
2025-06-10 22:38:47 | INFO     | backend.app:startup_event:113 - Initializing MongoDB database...
2025-06-10 22:38:47 | INFO     | backend.database:init_database:50 - Initializing MongoDB database...
2025-06-10 22:38:47 | SUCCESS  | backend.database:init_database:81 - MongoDB database initialized successfully: motion_agent
2025-06-10 22:38:47 | INFO     | backend.app:startup_event:120 - Creating database indexes...
2025-06-10 22:38:47 | INFO     | backend.database:create_indexes:259 - Creating database indexes...
2025-06-10 22:38:47 | SUCCESS  | backend.database:create_indexes:279 - Database indexes created successfully
2025-06-10 22:38:47 | INFO     | backend.app:startup_event:124 - Initializing taskiq...
2025-06-10 22:38:47 | INFO     | backend.tasks:init_taskiq:89 - Initializing taskiq...
2025-06-10 22:38:47 | SUCCESS  | backend.tasks:init_taskiq:93 - Taskiq initialized successfully
2025-06-10 22:38:47 | SUCCESS  | backend.app:startup_event:134 - Motion Agent API with Taskiq initialized successfully
2025-06-10 22:38:47 | INFO     | backend.app:startup_event:135 - Motion Agent API is ready to serve requests
2025-06-10 22:39:24 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 22:39:24 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 22:39:42 | INFO     | backend.routers.conversations:create_conversation:51 - Creating new conversation: New Conversation 6/10/2025, 10:39:42 PM
2025-06-10 22:39:42 | INFO     | backend.services.conversation_service:create_conversation:54 - Created new conversation: 684843ae106b125c52ff7639
2025-06-10 22:39:42 | SUCCESS  | backend.routers.conversations:create_conversation:55 - Created conversation: 684843ae106b125c52ff7639
2025-06-10 22:39:52 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 22:39:55 | ERROR    | backend.routers.conversations:send_message:303 - Failed to send message to conversation 684843ae106b125c52ff7639: 'TaskService' object has no attribute 'submit_conversation_task'
2025-06-10 22:40:14 | INFO     | backend.routers.animation:generate_professional_animation:108 - Received professional animation request: 生成一个 关羽登场 的场景，要求如下：
1. 全景，推镜，黄沙漫天，镜头缓缓推进女主，女主在画面中艰难前行，四周响起从快到慢的战鼓声，顺着声音镜头上摇，遮天蔽日的黄沙逐渐散去，时长 8s,声音 风沙声，战鼓声
2. 全景，缓推镜，风沙逐渐散开后出现伟岸的关羽骑士的轮廓，战鼓声逐渐加强。也出现士兵的呐喊声。时长2时。声音战鼓声，呐喊声
2025-06-10 22:40:14 | INFO     | backend.services.task_service:create_task:69 - Created task: 056b7208-82f2-4d24-aa6f-7462a4faaee8 (animation_generation)
2025-06-10 22:40:14 | INFO     | backend.services.task_service:create_animation_task:223 - Created animation task: 056b7208-82f2-4d24-aa6f-7462a4faaee8
2025-06-10 22:40:14 | INFO     | backend.routers.animation:generate_professional_animation:127 - Created animation task: 056b7208-82f2-4d24-aa6f-7462a4faaee8
2025-06-10 22:40:14 | INFO     | backend.services.task_service:update_task_status:262 - Updated task 056b7208-82f2-4d24-aa6f-7462a4faaee8 status to running
2025-06-10 22:40:16 | WARNING  | backend.animation.professional_nlu:_load_spacy_model:55 - No spaCy model found, using blank model
2025-06-10 22:40:16 | INFO     | backend.animation.professional_nlu:_load_sentiment_model:64 - Using rule-based sentiment analysis instead of large model
2025-06-10 22:40:16 | INFO     | backend.animation.professional_nlu:_load_action_classifier:74 - Using rule-based action classification instead of large model
2025-06-10 22:40:16 | INFO     | backend.animation.professional_nlu:__init__:41 - Professional Animator NLU initialized
2025-06-10 22:40:16 | INFO     | backend.animation.animator_functions:__init__:37 - Unified Professional Animator initialized
2025-06-10 22:40:16 | INFO     | backend.animation.professional_pipeline:__init__:84 - Professional Animation Pipeline initialized with enhanced features
2025-06-10 22:40:16 | WARNING  | backend.animation.professional_nlu:_load_spacy_model:55 - No spaCy model found, using blank model
2025-06-10 22:40:16 | INFO     | backend.animation.professional_nlu:_load_sentiment_model:64 - Using rule-based sentiment analysis instead of large model
2025-06-10 22:40:16 | INFO     | backend.animation.professional_nlu:_load_action_classifier:74 - Using rule-based action classification instead of large model
2025-06-10 22:40:16 | INFO     | backend.animation.professional_nlu:__init__:41 - Professional Animator NLU initialized
2025-06-10 22:40:16 | INFO     | backend.animation.animator_functions:__init__:37 - Unified Professional Animator initialized
2025-06-10 22:40:16 | INFO     | backend.animation.professional_pipeline:__init__:84 - Professional Animation Pipeline initialized with enhanced features
2025-06-10 22:40:16 | INFO     | backend.animation.professional_pipeline:process_animation_request:93 - Processing animation request: 生成一个 关羽登场 的场景，要求如下：
1. 全景，推镜，黄沙漫天，镜头缓缓推进女主，女主在画面中艰难前行，四周响起从快到慢的战鼓声，顺着声音镜头上摇，遮天蔽日的黄沙逐渐散去，时长 8s,声音 风沙声，战鼓声
2. 全景，缓推镜，风沙逐渐散开后出现伟岸的关羽骑士的轮廓，战鼓声逐渐加强。也出现士兵的呐喊声。时长2时。声音战鼓声，呐喊声
2025-06-10 22:40:16 | INFO     | backend.animation.professional_pipeline:process_animation_request:97 - Step 1: Natural Language Understanding
2025-06-10 22:40:16 | INFO     | backend.animation.professional_nlu:process_natural_language:397 - Processing animation request: 生成一个 关羽登场 的场景，要求如下：
1. 全景，推镜，黄沙漫天，镜头缓缓推进女主，女主在画面中艰难前行，四周响起从快到慢的战鼓声，顺着声音镜头上摇，遮天蔽日的黄沙逐渐散去，时长 8s,声音 风沙声，战鼓声
2. 全景，缓推镜，风沙逐渐散开后出现伟岸的关羽骑士的轮廓，战鼓声逐渐加强。也出现士兵的呐喊声。时长2时。声音战鼓声，呐喊声
2025-06-10 22:40:16 | SUCCESS  | backend.animation.professional_nlu:process_natural_language:426 - Successfully processed 1 actions
2025-06-10 22:40:16 | INFO     | backend.animation.professional_pipeline:process_animation_request:104 - Step 2: Animator Function Processing
2025-06-10 22:40:16 | INFO     | backend.animation.professional_pipeline:_apply_animator_functions:179 - Applying unified animator functions with enhanced features
2025-06-10 22:40:16 | INFO     | backend.animation.animator_functions:create_idle_animation:164 - Creating idle animation: stand
2025-06-10 22:40:16 | INFO     | backend.animation.professional_pipeline:_apply_animator_functions:190 - Optimizing action transitions
2025-06-10 22:40:16 | INFO     | backend.animation.transition_optimizer:optimize_action_sequence:146 - Optimizing sequence of 1 actions...
2025-06-10 22:40:16 | INFO     | backend.animation.professional_pipeline:process_animation_request:110 - Step 3: Generate Blender Animation Data
2025-06-10 22:40:16 | INFO     | backend.animation.professional_pipeline:process_animation_request:114 - Step 4: Execute Blender Animation Generation
2025-06-10 22:40:16 | INFO     | backend.animation.professional_pipeline:_execute_blender_generation:373 - Executing Blender command: /Applications/Blender.app/Contents/MacOS/Blender --background --python blender_scripts/generate_animation.py -- --input temp/animation_data/animation_data_1749566416.json --output output/animations/animation_default_1749566416.fbx --format fbx
2025-06-10 22:40:17 | SUCCESS  | backend.animation.professional_pipeline:_execute_blender_generation:385 - Blender animation generated: output/animations/animation_default_1749566416.fbx
2025-06-10 22:40:17 | WARNING  | backend.animation.professional_pipeline:process_animation_request:119 - Blender generation failed, creating test FBX file
2025-06-10 22:40:17 | INFO     | backend.animation.professional_pipeline:_create_test_fbx_file:570 - Created test FBX file: output/animations/animation_1749566417.fbx
2025-06-10 22:40:17 | INFO     | backend.animation.professional_pipeline:process_animation_request:125 - Step 5: FBX File Validation
2025-06-10 22:40:17 | INFO     | backend.animation.fbx_validator:validate_fbx_file:26 - Starting FBX validation for: output/animations/animation_1749566417.fbx
2025-06-10 22:40:17 | SUCCESS  | backend.animation.fbx_validator:validate_fbx_file:85 - FBX validation completed for: output/animations/animation_1749566417.fbx
2025-06-10 22:40:17 | INFO     | backend.animation.professional_pipeline:process_animation_request:134 - Step 6: Animation Quality Check
2025-06-10 22:40:17 | INFO     | backend.animation.quality_checker:check_animation_quality:130 - Starting animation quality check...
2025-06-10 22:40:17 | SUCCESS  | backend.animation.quality_checker:check_animation_quality:171 - Quality check completed. Overall score: 0.67
2025-06-10 22:40:17 | SUCCESS  | backend.animation.professional_pipeline:process_animation_request:159 - Animation generated successfully in 0.81s
2025-06-10 22:40:17 | INFO     | backend.services.task_service:update_task_status:262 - Updated task 056b7208-82f2-4d24-aa6f-7462a4faaee8 status to completed
2025-06-10 22:40:17 | INFO     | backend.routers.animation:generate_professional_animation:205 - Updated task 056b7208-82f2-4d24-aa6f-7462a4faaee8 status to completed
2025-06-10 22:40:17 | SUCCESS  | backend.routers.animation:generate_professional_animation:209 - Professional animation generation completed
2025-06-10 22:40:20 | INFO     | backend.routers.animation:generate_professional_animation:108 - Received professional animation request: Animation generated: undefined
2025-06-10 22:40:20 | INFO     | backend.services.task_service:create_task:69 - Created task: 7bbced50-afc3-4d50-8ada-d89f04b1191a (animation_generation)
2025-06-10 22:40:20 | INFO     | backend.services.task_service:create_animation_task:223 - Created animation task: 7bbced50-afc3-4d50-8ada-d89f04b1191a
2025-06-10 22:40:20 | INFO     | backend.routers.animation:generate_professional_animation:127 - Created animation task: 7bbced50-afc3-4d50-8ada-d89f04b1191a
2025-06-10 22:40:20 | INFO     | backend.services.task_service:update_task_status:262 - Updated task 7bbced50-afc3-4d50-8ada-d89f04b1191a status to running
2025-06-10 22:40:20 | WARNING  | backend.animation.professional_nlu:_load_spacy_model:55 - No spaCy model found, using blank model
2025-06-10 22:40:20 | INFO     | backend.animation.professional_nlu:_load_sentiment_model:64 - Using rule-based sentiment analysis instead of large model
2025-06-10 22:40:20 | INFO     | backend.animation.professional_nlu:_load_action_classifier:74 - Using rule-based action classification instead of large model
2025-06-10 22:40:20 | INFO     | backend.animation.professional_nlu:__init__:41 - Professional Animator NLU initialized
2025-06-10 22:40:20 | INFO     | backend.animation.animator_functions:__init__:37 - Unified Professional Animator initialized
2025-06-10 22:40:20 | INFO     | backend.animation.professional_pipeline:__init__:84 - Professional Animation Pipeline initialized with enhanced features
2025-06-10 22:40:20 | INFO     | backend.animation.professional_pipeline:process_animation_request:93 - Processing animation request: Animation generated: undefined
2025-06-10 22:40:20 | INFO     | backend.animation.professional_pipeline:process_animation_request:97 - Step 1: Natural Language Understanding
2025-06-10 22:40:20 | INFO     | backend.animation.professional_nlu:process_natural_language:397 - Processing animation request: Animation generated: undefined
2025-06-10 22:40:20 | SUCCESS  | backend.animation.professional_nlu:process_natural_language:426 - Successfully processed 1 actions
2025-06-10 22:40:20 | INFO     | backend.animation.professional_pipeline:process_animation_request:104 - Step 2: Animator Function Processing
2025-06-10 22:40:20 | INFO     | backend.animation.professional_pipeline:_apply_animator_functions:179 - Applying unified animator functions with enhanced features
2025-06-10 22:40:20 | INFO     | backend.animation.animator_functions:create_idle_animation:164 - Creating idle animation: stand
2025-06-10 22:40:20 | INFO     | backend.animation.professional_pipeline:_apply_animator_functions:190 - Optimizing action transitions
2025-06-10 22:40:20 | INFO     | backend.animation.transition_optimizer:optimize_action_sequence:146 - Optimizing sequence of 1 actions...
2025-06-10 22:40:20 | INFO     | backend.animation.professional_pipeline:process_animation_request:110 - Step 3: Generate Blender Animation Data
2025-06-10 22:40:20 | INFO     | backend.animation.professional_pipeline:process_animation_request:114 - Step 4: Execute Blender Animation Generation
2025-06-10 22:40:20 | INFO     | backend.animation.professional_pipeline:_execute_blender_generation:373 - Executing Blender command: /Applications/Blender.app/Contents/MacOS/Blender --background --python blender_scripts/generate_animation.py -- --input temp/animation_data/animation_data_1749566420.json --output output/animations/animation_default_1749566420.fbx --format fbx
2025-06-10 22:40:20 | SUCCESS  | backend.animation.professional_pipeline:_execute_blender_generation:385 - Blender animation generated: output/animations/animation_default_1749566420.fbx
2025-06-10 22:40:20 | WARNING  | backend.animation.professional_pipeline:process_animation_request:119 - Blender generation failed, creating test FBX file
2025-06-10 22:40:20 | INFO     | backend.animation.professional_pipeline:_create_test_fbx_file:570 - Created test FBX file: output/animations/animation_1749566420.fbx
2025-06-10 22:40:20 | INFO     | backend.animation.professional_pipeline:process_animation_request:125 - Step 5: FBX File Validation
2025-06-10 22:40:20 | INFO     | backend.animation.fbx_validator:validate_fbx_file:26 - Starting FBX validation for: output/animations/animation_1749566420.fbx
2025-06-10 22:40:20 | SUCCESS  | backend.animation.fbx_validator:validate_fbx_file:85 - FBX validation completed for: output/animations/animation_1749566420.fbx
2025-06-10 22:40:20 | INFO     | backend.animation.professional_pipeline:process_animation_request:134 - Step 6: Animation Quality Check
2025-06-10 22:40:20 | INFO     | backend.animation.quality_checker:check_animation_quality:130 - Starting animation quality check...
2025-06-10 22:40:20 | SUCCESS  | backend.animation.quality_checker:check_animation_quality:171 - Quality check completed. Overall score: 0.67
2025-06-10 22:40:20 | SUCCESS  | backend.animation.professional_pipeline:process_animation_request:159 - Animation generated successfully in 0.38s
2025-06-10 22:40:20 | INFO     | backend.services.task_service:update_task_status:262 - Updated task 7bbced50-afc3-4d50-8ada-d89f04b1191a status to completed
2025-06-10 22:40:20 | INFO     | backend.routers.animation:generate_professional_animation:205 - Updated task 7bbced50-afc3-4d50-8ada-d89f04b1191a status to completed
2025-06-10 22:40:20 | SUCCESS  | backend.routers.animation:generate_professional_animation:209 - Professional animation generation completed
2025-06-10 22:40:22 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 22:40:24 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 22:40:52 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 22:41:22 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 22:41:24 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 22:42:24 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 22:42:24 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 22:43:24 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 22:43:24 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 22:43:53 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 22:44:12 | INFO     | backend.routers.animation:generate_professional_animation:108 - Received professional animation request: Animation generated: undefined
2025-06-10 22:44:12 | INFO     | backend.services.task_service:create_task:69 - Created task: 62899c9b-2db8-47a6-9036-dce483c840f6 (animation_generation)
2025-06-10 22:44:12 | INFO     | backend.services.task_service:create_animation_task:223 - Created animation task: 62899c9b-2db8-47a6-9036-dce483c840f6
2025-06-10 22:44:12 | INFO     | backend.routers.animation:generate_professional_animation:127 - Created animation task: 62899c9b-2db8-47a6-9036-dce483c840f6
2025-06-10 22:44:12 | INFO     | backend.services.task_service:update_task_status:262 - Updated task 62899c9b-2db8-47a6-9036-dce483c840f6 status to running
2025-06-10 22:44:12 | WARNING  | backend.animation.professional_nlu:_load_spacy_model:55 - No spaCy model found, using blank model
2025-06-10 22:44:12 | INFO     | backend.animation.professional_nlu:_load_sentiment_model:64 - Using rule-based sentiment analysis instead of large model
2025-06-10 22:44:12 | INFO     | backend.animation.professional_nlu:_load_action_classifier:74 - Using rule-based action classification instead of large model
2025-06-10 22:44:12 | INFO     | backend.animation.professional_nlu:__init__:41 - Professional Animator NLU initialized
2025-06-10 22:44:12 | INFO     | backend.animation.animator_functions:__init__:37 - Unified Professional Animator initialized
2025-06-10 22:44:12 | INFO     | backend.animation.professional_pipeline:__init__:84 - Professional Animation Pipeline initialized with enhanced features
2025-06-10 22:44:12 | INFO     | backend.animation.professional_pipeline:process_animation_request:93 - Processing animation request: Animation generated: undefined
2025-06-10 22:44:12 | INFO     | backend.animation.professional_pipeline:process_animation_request:97 - Step 1: Natural Language Understanding
2025-06-10 22:44:12 | INFO     | backend.animation.professional_nlu:process_natural_language:397 - Processing animation request: Animation generated: undefined
2025-06-10 22:44:12 | SUCCESS  | backend.animation.professional_nlu:process_natural_language:426 - Successfully processed 1 actions
2025-06-10 22:44:12 | INFO     | backend.animation.professional_pipeline:process_animation_request:104 - Step 2: Animator Function Processing
2025-06-10 22:44:12 | INFO     | backend.animation.professional_pipeline:_apply_animator_functions:179 - Applying unified animator functions with enhanced features
2025-06-10 22:44:12 | INFO     | backend.animation.animator_functions:create_idle_animation:164 - Creating idle animation: stand
2025-06-10 22:44:12 | INFO     | backend.animation.professional_pipeline:_apply_animator_functions:190 - Optimizing action transitions
2025-06-10 22:44:12 | INFO     | backend.animation.transition_optimizer:optimize_action_sequence:146 - Optimizing sequence of 1 actions...
2025-06-10 22:44:12 | INFO     | backend.animation.professional_pipeline:process_animation_request:110 - Step 3: Generate Blender Animation Data
2025-06-10 22:44:12 | INFO     | backend.animation.professional_pipeline:process_animation_request:114 - Step 4: Execute Blender Animation Generation
2025-06-10 22:44:12 | INFO     | backend.animation.professional_pipeline:_execute_blender_generation:373 - Executing Blender command: /Applications/Blender.app/Contents/MacOS/Blender --background --python blender_scripts/generate_animation.py -- --input temp/animation_data/animation_data_1749566652.json --output output/animations/animation_default_1749566652.fbx --format fbx
2025-06-10 22:44:13 | SUCCESS  | backend.animation.professional_pipeline:_execute_blender_generation:385 - Blender animation generated: output/animations/animation_default_1749566652.fbx
2025-06-10 22:44:13 | WARNING  | backend.animation.professional_pipeline:process_animation_request:119 - Blender generation failed, creating test FBX file
2025-06-10 22:44:13 | INFO     | backend.animation.professional_pipeline:_create_test_fbx_file:570 - Created test FBX file: output/animations/animation_1749566653.fbx
2025-06-10 22:44:13 | INFO     | backend.animation.professional_pipeline:process_animation_request:125 - Step 5: FBX File Validation
2025-06-10 22:44:13 | INFO     | backend.animation.fbx_validator:validate_fbx_file:26 - Starting FBX validation for: output/animations/animation_1749566653.fbx
2025-06-10 22:44:13 | SUCCESS  | backend.animation.fbx_validator:validate_fbx_file:85 - FBX validation completed for: output/animations/animation_1749566653.fbx
2025-06-10 22:44:13 | INFO     | backend.animation.professional_pipeline:process_animation_request:134 - Step 6: Animation Quality Check
2025-06-10 22:44:13 | INFO     | backend.animation.quality_checker:check_animation_quality:130 - Starting animation quality check...
2025-06-10 22:44:13 | SUCCESS  | backend.animation.quality_checker:check_animation_quality:171 - Quality check completed. Overall score: 0.67
2025-06-10 22:44:13 | SUCCESS  | backend.animation.professional_pipeline:process_animation_request:159 - Animation generated successfully in 0.75s
2025-06-10 22:44:13 | INFO     | backend.services.task_service:update_task_status:262 - Updated task 62899c9b-2db8-47a6-9036-dce483c840f6 status to completed
2025-06-10 22:44:13 | INFO     | backend.routers.animation:generate_professional_animation:205 - Updated task 62899c9b-2db8-47a6-9036-dce483c840f6 status to completed
2025-06-10 22:44:13 | SUCCESS  | backend.routers.animation:generate_professional_animation:209 - Professional animation generation completed
2025-06-10 22:44:22 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 22:44:24 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 22:44:52 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 22:45:22 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 22:45:24 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 22:46:24 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 22:46:24 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 22:47:24 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 22:47:24 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 22:48:24 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 22:48:24 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 22:49:24 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 22:49:24 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 22:50:24 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 22:50:24 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 22:51:24 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 22:51:24 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 22:51:52 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 22:52:22 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 22:52:24 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 22:52:52 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 22:53:22 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 22:53:24 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 22:53:52 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 11:03:28 | SUCCESS  | backend.config:validate_config:309 - Configuration validation passed
2025-06-11 11:04:40 | SUCCESS  | backend.config:validate_config:309 - Configuration validation passed
2025-06-11 11:04:40 | INFO     | backend.app:lifespan:84 - Starting Motion Agent API server with Taskiq...
2025-06-11 11:04:40 | INFO     | backend.app:lifespan:87 - Initializing MongoDB database...
2025-06-11 11:04:40 | INFO     | backend.database:init_database:50 - Initializing MongoDB database...
2025-06-11 11:04:46 | ERROR    | backend.database:init_database:85 - Failed to initialize MongoDB database: localhost:27017: [Errno 61] Connection refused (configured timeouts: socketTimeoutMS: 10000.0ms, connectTimeoutMS: 10000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 6848f24811ed562582a44427, topology_type: Unknown, servers: [<ServerDescription ('localhost', 27017) server_type: Unknown, rtt: None, error=AutoReconnect('localhost:27017: [Errno 61] Connection refused (configured timeouts: socketTimeoutMS: 10000.0ms, connectTimeoutMS: 10000.0ms)')>]>
2025-06-11 11:04:46 | ERROR    | backend.app:lifespan:90 - Failed to initialize MongoDB database
2025-06-11 11:04:46 | INFO     | backend.app:lifespan:98 - Initializing taskiq...
2025-06-11 11:04:46 | INFO     | backend.tasks:init_taskiq:89 - Initializing taskiq...
2025-06-11 11:04:46 | SUCCESS  | backend.tasks:init_taskiq:93 - Taskiq initialized successfully
2025-06-11 11:04:46 | SUCCESS  | backend.app:lifespan:108 - Motion Agent API with Taskiq initialized successfully
2025-06-11 11:04:46 | INFO     | backend.app:lifespan:109 - Motion Agent API is ready to serve requests
2025-06-11 11:04:47 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 11:04:47 | INFO     | backend.routers.conversations:create_conversation:51 - Creating new conversation: New Conversation 6/11/2025, 11:04:47 AM
2025-06-11 11:04:47 | ERROR    | backend.services.conversation_service:create_conversation:59 - Failed to create conversation: 
2025-06-11 11:04:47 | ERROR    | backend.routers.conversations:create_conversation:59 - Failed to create conversation: 
2025-06-11 11:05:05 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 11:05:15 | ERROR    | backend.services.conversation_service:list_conversations:200 - Failed to list conversations: 
2025-06-11 11:05:15 | ERROR    | backend.routers.conversations:list_conversations:111 - Failed to list conversations: 
2025-06-11 11:05:15 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 11:05:15 | INFO     | backend.routers.conversations:create_conversation:51 - Creating new conversation: New Conversation 6/11/2025, 11:05:15 AM
2025-06-11 11:05:15 | ERROR    | backend.services.conversation_service:create_conversation:59 - Failed to create conversation: 
2025-06-11 11:05:15 | ERROR    | backend.routers.conversations:create_conversation:59 - Failed to create conversation: 
2025-06-11 11:05:20 | INFO     | backend.routers.conversations:create_conversation:51 - Creating new conversation: New Conversation 6/11/2025, 11:05:20 AM
2025-06-11 11:05:20 | ERROR    | backend.services.conversation_service:create_conversation:59 - Failed to create conversation: 
2025-06-11 11:05:20 | ERROR    | backend.routers.conversations:create_conversation:59 - Failed to create conversation: 
2025-06-11 11:05:23 | INFO     | backend.app:lifespan:113 - Shutting down Motion Agent API...
2025-06-11 11:05:23 | INFO     | backend.tasks:shutdown_taskiq:104 - Shutting down taskiq...
2025-06-11 11:05:23 | SUCCESS  | backend.tasks:shutdown_taskiq:106 - Taskiq shutdown successfully
2025-06-11 11:05:23 | INFO     | backend.database:close_database:94 - Closing MongoDB connections...
2025-06-11 11:05:23 | SUCCESS  | backend.database:close_database:101 - MongoDB connections closed
2025-06-11 11:05:23 | SUCCESS  | backend.app:lifespan:121 - Motion Agent API shutdown completed
2025-06-11 11:05:35 | SUCCESS  | backend.config:validate_config:309 - Configuration validation passed
2025-06-11 11:05:35 | INFO     | backend.app:lifespan:84 - Starting Motion Agent API server with Taskiq...
2025-06-11 11:05:35 | INFO     | backend.app:lifespan:87 - Initializing MongoDB database...
2025-06-11 11:05:35 | INFO     | backend.database:init_database:50 - Initializing MongoDB database...
2025-06-11 11:05:40 | ERROR    | backend.database:init_database:85 - Failed to initialize MongoDB database: localhost:27017: connection closed (configured timeouts: socketTimeoutMS: 10000.0ms, connectTimeoutMS: 10000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 6848f27f200909d37f98b87d, topology_type: Unknown, servers: [<ServerDescription ('localhost', 27017) server_type: Unknown, rtt: None, error=AutoReconnect('localhost:27017: connection closed (configured timeouts: socketTimeoutMS: 10000.0ms, connectTimeoutMS: 10000.0ms)')>]>
2025-06-11 11:05:40 | ERROR    | backend.app:lifespan:90 - Failed to initialize MongoDB database
2025-06-11 11:05:40 | INFO     | backend.app:lifespan:98 - Initializing taskiq...
2025-06-11 11:05:40 | INFO     | backend.tasks:init_taskiq:89 - Initializing taskiq...
2025-06-11 11:05:40 | SUCCESS  | backend.tasks:init_taskiq:93 - Taskiq initialized successfully
2025-06-11 11:05:40 | SUCCESS  | backend.app:lifespan:108 - Motion Agent API with Taskiq initialized successfully
2025-06-11 11:05:40 | INFO     | backend.app:lifespan:109 - Motion Agent API is ready to serve requests
2025-06-11 11:05:41 | INFO     | backend.routers.conversations:create_conversation:51 - Creating new conversation: New Conversation 6/11/2025, 11:05:41 AM
2025-06-11 11:05:41 | ERROR    | backend.services.conversation_service:create_conversation:59 - Failed to create conversation: 
2025-06-11 11:05:41 | ERROR    | backend.routers.conversations:create_conversation:59 - Failed to create conversation: 
2025-06-11 11:05:43 | ERROR    | backend.services.conversation_service:list_conversations:200 - Failed to list conversations: 
2025-06-11 11:05:43 | ERROR    | backend.routers.conversations:list_conversations:111 - Failed to list conversations: 
2025-06-11 11:05:43 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 11:05:43 | INFO     | backend.routers.conversations:create_conversation:51 - Creating new conversation: New Conversation 6/11/2025, 11:05:43 AM
2025-06-11 11:05:43 | ERROR    | backend.services.conversation_service:create_conversation:59 - Failed to create conversation: 
2025-06-11 11:05:43 | ERROR    | backend.routers.conversations:create_conversation:59 - Failed to create conversation: 
2025-06-11 11:06:13 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 11:06:26 | INFO     | backend.app:lifespan:113 - Shutting down Motion Agent API...
2025-06-11 11:06:26 | INFO     | backend.tasks:shutdown_taskiq:104 - Shutting down taskiq...
2025-06-11 11:06:26 | SUCCESS  | backend.tasks:shutdown_taskiq:106 - Taskiq shutdown successfully
2025-06-11 11:06:26 | INFO     | backend.database:close_database:94 - Closing MongoDB connections...
2025-06-11 11:06:26 | SUCCESS  | backend.database:close_database:101 - MongoDB connections closed
2025-06-11 11:06:26 | SUCCESS  | backend.app:lifespan:121 - Motion Agent API shutdown completed
2025-06-11 11:06:27 | SUCCESS  | backend.config:validate_config:309 - Configuration validation passed
2025-06-11 11:06:27 | INFO     | backend.app:lifespan:84 - Starting Motion Agent API server with Taskiq...
2025-06-11 11:06:27 | INFO     | backend.app:lifespan:87 - Initializing MongoDB database...
2025-06-11 11:06:27 | INFO     | backend.database:init_database:50 - Initializing MongoDB database...
2025-06-11 11:06:32 | ERROR    | backend.database:init_database:85 - Failed to initialize MongoDB database: localhost:27017: connection closed (configured timeouts: socketTimeoutMS: 10000.0ms, connectTimeoutMS: 10000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 6848f2b345862db7c3b854a6, topology_type: Unknown, servers: [<ServerDescription ('localhost', 27017) server_type: Unknown, rtt: None, error=AutoReconnect('localhost:27017: connection closed (configured timeouts: socketTimeoutMS: 10000.0ms, connectTimeoutMS: 10000.0ms)')>]>
2025-06-11 11:06:32 | ERROR    | backend.app:lifespan:90 - Failed to initialize MongoDB database
2025-06-11 11:06:32 | INFO     | backend.app:lifespan:98 - Initializing taskiq...
2025-06-11 11:06:32 | INFO     | backend.tasks:init_taskiq:89 - Initializing taskiq...
2025-06-11 11:06:32 | SUCCESS  | backend.tasks:init_taskiq:93 - Taskiq initialized successfully
2025-06-11 11:06:32 | SUCCESS  | backend.app:lifespan:108 - Motion Agent API with Taskiq initialized successfully
2025-06-11 11:06:32 | INFO     | backend.app:lifespan:109 - Motion Agent API is ready to serve requests
2025-06-11 11:06:36 | INFO     | backend.app:lifespan:113 - Shutting down Motion Agent API...
2025-06-11 11:06:36 | INFO     | backend.tasks:shutdown_taskiq:104 - Shutting down taskiq...
2025-06-11 11:06:36 | SUCCESS  | backend.tasks:shutdown_taskiq:106 - Taskiq shutdown successfully
2025-06-11 11:06:36 | INFO     | backend.database:close_database:94 - Closing MongoDB connections...
2025-06-11 11:06:36 | SUCCESS  | backend.database:close_database:101 - MongoDB connections closed
2025-06-11 11:06:36 | SUCCESS  | backend.app:lifespan:121 - Motion Agent API shutdown completed
2025-06-11 11:06:36 | SUCCESS  | backend.config:validate_config:309 - Configuration validation passed
2025-06-11 11:06:36 | INFO     | backend.app:lifespan:84 - Starting Motion Agent API server with Taskiq...
2025-06-11 11:06:36 | INFO     | backend.app:lifespan:87 - Initializing MongoDB database...
2025-06-11 11:06:36 | INFO     | backend.database:init_database:50 - Initializing MongoDB database...
2025-06-11 11:06:41 | ERROR    | backend.database:init_database:85 - Failed to initialize MongoDB database: localhost:27017: connection closed (configured timeouts: socketTimeoutMS: 10000.0ms, connectTimeoutMS: 10000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 6848f2bc5648f2d3dfae93a9, topology_type: Unknown, servers: [<ServerDescription ('localhost', 27017) server_type: Unknown, rtt: None, error=AutoReconnect('localhost:27017: connection closed (configured timeouts: socketTimeoutMS: 10000.0ms, connectTimeoutMS: 10000.0ms)')>]>
2025-06-11 11:06:41 | ERROR    | backend.app:lifespan:90 - Failed to initialize MongoDB database
2025-06-11 11:06:41 | INFO     | backend.app:lifespan:98 - Initializing taskiq...
2025-06-11 11:06:41 | INFO     | backend.tasks:init_taskiq:89 - Initializing taskiq...
2025-06-11 11:06:41 | SUCCESS  | backend.tasks:init_taskiq:93 - Taskiq initialized successfully
2025-06-11 11:06:41 | SUCCESS  | backend.app:lifespan:108 - Motion Agent API with Taskiq initialized successfully
2025-06-11 11:06:41 | INFO     | backend.app:lifespan:109 - Motion Agent API is ready to serve requests
2025-06-11 11:06:41 | SUCCESS  | backend.config:validate_config:309 - Configuration validation passed
2025-06-11 11:06:41 | INFO     | backend.app:lifespan:84 - Starting Motion Agent API server with Taskiq...
2025-06-11 11:06:41 | INFO     | backend.app:lifespan:87 - Initializing MongoDB database...
2025-06-11 11:06:41 | INFO     | backend.database:init_database:50 - Initializing MongoDB database...
2025-06-11 11:06:46 | ERROR    | backend.database:init_database:85 - Failed to initialize MongoDB database: localhost:27017: connection closed (configured timeouts: socketTimeoutMS: 10000.0ms, connectTimeoutMS: 10000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 6848f2c1aca384a8e6a6c181, topology_type: Unknown, servers: [<ServerDescription ('localhost', 27017) server_type: Unknown, rtt: None, error=AutoReconnect('localhost:27017: connection closed (configured timeouts: socketTimeoutMS: 10000.0ms, connectTimeoutMS: 10000.0ms)')>]>
2025-06-11 11:06:46 | ERROR    | backend.app:lifespan:90 - Failed to initialize MongoDB database
2025-06-11 11:06:46 | INFO     | backend.app:lifespan:98 - Initializing taskiq...
2025-06-11 11:06:46 | INFO     | backend.tasks:init_taskiq:89 - Initializing taskiq...
2025-06-11 11:06:46 | SUCCESS  | backend.tasks:init_taskiq:93 - Taskiq initialized successfully
2025-06-11 11:06:46 | SUCCESS  | backend.app:lifespan:108 - Motion Agent API with Taskiq initialized successfully
2025-06-11 11:06:46 | INFO     | backend.app:lifespan:109 - Motion Agent API is ready to serve requests
2025-06-11 11:06:47 | SUCCESS  | backend.config:validate_config:309 - Configuration validation passed
2025-06-11 11:06:47 | INFO     | backend.app:lifespan:84 - Starting Motion Agent API server with Taskiq...
2025-06-11 11:06:47 | INFO     | backend.app:lifespan:87 - Initializing MongoDB database...
2025-06-11 11:06:47 | INFO     | backend.database:init_database:50 - Initializing MongoDB database...
2025-06-11 11:06:52 | ERROR    | backend.database:init_database:85 - Failed to initialize MongoDB database: localhost:27017: connection closed (configured timeouts: socketTimeoutMS: 10000.0ms, connectTimeoutMS: 10000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 6848f2c790589857c4e661a4, topology_type: Unknown, servers: [<ServerDescription ('localhost', 27017) server_type: Unknown, rtt: None, error=AutoReconnect('localhost:27017: connection closed (configured timeouts: socketTimeoutMS: 10000.0ms, connectTimeoutMS: 10000.0ms)')>]>
2025-06-11 11:06:52 | ERROR    | backend.app:lifespan:90 - Failed to initialize MongoDB database
2025-06-11 11:06:52 | INFO     | backend.app:lifespan:98 - Initializing taskiq...
2025-06-11 11:06:52 | INFO     | backend.tasks:init_taskiq:89 - Initializing taskiq...
2025-06-11 11:06:52 | SUCCESS  | backend.tasks:init_taskiq:93 - Taskiq initialized successfully
2025-06-11 11:06:52 | SUCCESS  | backend.app:lifespan:108 - Motion Agent API with Taskiq initialized successfully
2025-06-11 11:06:52 | INFO     | backend.app:lifespan:109 - Motion Agent API is ready to serve requests
2025-06-11 11:06:52 | SUCCESS  | backend.config:validate_config:309 - Configuration validation passed
2025-06-11 11:06:52 | INFO     | backend.app:lifespan:84 - Starting Motion Agent API server with Taskiq...
2025-06-11 11:06:52 | INFO     | backend.app:lifespan:87 - Initializing MongoDB database...
2025-06-11 11:06:52 | INFO     | backend.database:init_database:50 - Initializing MongoDB database...
2025-06-11 11:06:57 | ERROR    | backend.database:init_database:85 - Failed to initialize MongoDB database: localhost:27017: connection closed (configured timeouts: socketTimeoutMS: 10000.0ms, connectTimeoutMS: 10000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 6848f2cc6621560d4a15607c, topology_type: Unknown, servers: [<ServerDescription ('localhost', 27017) server_type: Unknown, rtt: None, error=AutoReconnect('localhost:27017: connection closed (configured timeouts: socketTimeoutMS: 10000.0ms, connectTimeoutMS: 10000.0ms)')>]>
2025-06-11 11:06:57 | ERROR    | backend.app:lifespan:90 - Failed to initialize MongoDB database
2025-06-11 11:06:57 | INFO     | backend.app:lifespan:98 - Initializing taskiq...
2025-06-11 11:06:57 | INFO     | backend.tasks:init_taskiq:89 - Initializing taskiq...
2025-06-11 11:06:57 | SUCCESS  | backend.tasks:init_taskiq:93 - Taskiq initialized successfully
2025-06-11 11:06:57 | SUCCESS  | backend.app:lifespan:108 - Motion Agent API with Taskiq initialized successfully
2025-06-11 11:06:57 | INFO     | backend.app:lifespan:109 - Motion Agent API is ready to serve requests
2025-06-11 11:06:57 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 11:07:13 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 11:07:43 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 11:08:03 | INFO     | backend.app:lifespan:113 - Shutting down Motion Agent API...
2025-06-11 11:08:03 | INFO     | backend.tasks:shutdown_taskiq:104 - Shutting down taskiq...
2025-06-11 11:08:03 | SUCCESS  | backend.tasks:shutdown_taskiq:106 - Taskiq shutdown successfully
2025-06-11 11:08:03 | INFO     | backend.database:close_database:94 - Closing MongoDB connections...
2025-06-11 11:08:03 | SUCCESS  | backend.database:close_database:101 - MongoDB connections closed
2025-06-11 11:08:03 | SUCCESS  | backend.app:lifespan:121 - Motion Agent API shutdown completed
2025-06-11 11:08:03 | SUCCESS  | backend.config:validate_config:309 - Configuration validation passed
2025-06-11 11:08:03 | INFO     | backend.app:lifespan:84 - Starting Motion Agent API server with Taskiq...
2025-06-11 11:08:03 | INFO     | backend.app:lifespan:87 - Initializing MongoDB database...
2025-06-11 11:08:03 | INFO     | backend.database:init_database:50 - Initializing MongoDB database...
2025-06-11 11:08:08 | ERROR    | backend.database:init_database:85 - Failed to initialize MongoDB database: localhost:27017: connection closed (configured timeouts: socketTimeoutMS: 10000.0ms, connectTimeoutMS: 10000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 6848f3134001d1502a62e8bc, topology_type: Unknown, servers: [<ServerDescription ('localhost', 27017) server_type: Unknown, rtt: None, error=AutoReconnect('localhost:27017: connection closed (configured timeouts: socketTimeoutMS: 10000.0ms, connectTimeoutMS: 10000.0ms)')>]>
2025-06-11 11:08:08 | ERROR    | backend.app:lifespan:90 - Failed to initialize MongoDB database
2025-06-11 11:08:08 | INFO     | backend.app:lifespan:98 - Initializing taskiq...
2025-06-11 11:08:08 | INFO     | backend.tasks:init_taskiq:89 - Initializing taskiq...
2025-06-11 11:08:08 | SUCCESS  | backend.tasks:init_taskiq:93 - Taskiq initialized successfully
2025-06-11 11:08:08 | SUCCESS  | backend.app:lifespan:108 - Motion Agent API with Taskiq initialized successfully
2025-06-11 11:08:08 | INFO     | backend.app:lifespan:109 - Motion Agent API is ready to serve requests
2025-06-11 11:08:13 | INFO     | backend.app:lifespan:113 - Shutting down Motion Agent API...
2025-06-11 11:08:13 | INFO     | backend.tasks:shutdown_taskiq:104 - Shutting down taskiq...
2025-06-11 11:08:13 | SUCCESS  | backend.tasks:shutdown_taskiq:106 - Taskiq shutdown successfully
2025-06-11 11:08:13 | INFO     | backend.database:close_database:94 - Closing MongoDB connections...
2025-06-11 11:08:13 | SUCCESS  | backend.database:close_database:101 - MongoDB connections closed
2025-06-11 11:08:13 | SUCCESS  | backend.app:lifespan:121 - Motion Agent API shutdown completed
2025-06-11 11:08:13 | SUCCESS  | backend.config:validate_config:309 - Configuration validation passed
2025-06-11 11:08:13 | INFO     | backend.app:lifespan:84 - Starting Motion Agent API server with Taskiq...
2025-06-11 11:08:13 | INFO     | backend.app:lifespan:87 - Initializing MongoDB database...
2025-06-11 11:08:13 | INFO     | backend.database:init_database:50 - Initializing MongoDB database...
2025-06-11 11:08:18 | ERROR    | backend.database:init_database:85 - Failed to initialize MongoDB database: localhost:27017: connection closed (configured timeouts: socketTimeoutMS: 10000.0ms, connectTimeoutMS: 10000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 6848f31d5ab93178504cd707, topology_type: Unknown, servers: [<ServerDescription ('localhost', 27017) server_type: Unknown, rtt: None, error=AutoReconnect('localhost:27017: connection closed (configured timeouts: socketTimeoutMS: 10000.0ms, connectTimeoutMS: 10000.0ms)')>]>
2025-06-11 11:08:18 | ERROR    | backend.app:lifespan:90 - Failed to initialize MongoDB database
2025-06-11 11:08:18 | INFO     | backend.app:lifespan:98 - Initializing taskiq...
2025-06-11 11:08:18 | INFO     | backend.tasks:init_taskiq:89 - Initializing taskiq...
2025-06-11 11:08:18 | SUCCESS  | backend.tasks:init_taskiq:93 - Taskiq initialized successfully
2025-06-11 11:08:18 | SUCCESS  | backend.app:lifespan:108 - Motion Agent API with Taskiq initialized successfully
2025-06-11 11:08:18 | INFO     | backend.app:lifespan:109 - Motion Agent API is ready to serve requests
2025-06-11 11:08:19 | SUCCESS  | backend.config:validate_config:309 - Configuration validation passed
2025-06-11 11:08:19 | INFO     | backend.app:lifespan:84 - Starting Motion Agent API server with Taskiq...
2025-06-11 11:08:19 | INFO     | backend.app:lifespan:87 - Initializing MongoDB database...
2025-06-11 11:08:19 | INFO     | backend.database:init_database:50 - Initializing MongoDB database...
2025-06-11 11:08:24 | ERROR    | backend.database:init_database:85 - Failed to initialize MongoDB database: localhost:27017: connection closed (configured timeouts: socketTimeoutMS: 10000.0ms, connectTimeoutMS: 10000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 6848f32313a2ff5e7d0ff992, topology_type: Unknown, servers: [<ServerDescription ('localhost', 27017) server_type: Unknown, rtt: None, error=AutoReconnect('localhost:27017: connection closed (configured timeouts: socketTimeoutMS: 10000.0ms, connectTimeoutMS: 10000.0ms)')>]>
2025-06-11 11:08:24 | ERROR    | backend.app:lifespan:90 - Failed to initialize MongoDB database
2025-06-11 11:08:24 | INFO     | backend.app:lifespan:98 - Initializing taskiq...
2025-06-11 11:08:24 | INFO     | backend.tasks:init_taskiq:89 - Initializing taskiq...
2025-06-11 11:08:24 | SUCCESS  | backend.tasks:init_taskiq:93 - Taskiq initialized successfully
2025-06-11 11:08:24 | SUCCESS  | backend.app:lifespan:108 - Motion Agent API with Taskiq initialized successfully
2025-06-11 11:08:24 | INFO     | backend.app:lifespan:109 - Motion Agent API is ready to serve requests
2025-06-11 11:08:24 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 11:08:43 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 11:08:58 | INFO     | backend.app:lifespan:113 - Shutting down Motion Agent API...
2025-06-11 11:08:58 | INFO     | backend.tasks:shutdown_taskiq:104 - Shutting down taskiq...
2025-06-11 11:08:58 | SUCCESS  | backend.tasks:shutdown_taskiq:106 - Taskiq shutdown successfully
2025-06-11 11:08:58 | INFO     | backend.database:close_database:94 - Closing MongoDB connections...
2025-06-11 11:08:58 | SUCCESS  | backend.database:close_database:101 - MongoDB connections closed
2025-06-11 11:08:58 | SUCCESS  | backend.app:lifespan:121 - Motion Agent API shutdown completed
2025-06-11 11:08:58 | SUCCESS  | backend.config:validate_config:309 - Configuration validation passed
2025-06-11 11:08:58 | INFO     | backend.app:lifespan:78 - Starting Motion Agent API server with Taskiq...
2025-06-11 11:08:58 | INFO     | backend.app:lifespan:81 - Initializing MongoDB database...
2025-06-11 11:08:58 | INFO     | backend.database:init_database:50 - Initializing MongoDB database...
2025-06-11 11:09:03 | ERROR    | backend.database:init_database:85 - Failed to initialize MongoDB database: localhost:27017: connection closed (configured timeouts: socketTimeoutMS: 10000.0ms, connectTimeoutMS: 10000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 6848f34ad5a4a74e82b0f426, topology_type: Unknown, servers: [<ServerDescription ('localhost', 27017) server_type: Unknown, rtt: None, error=AutoReconnect('localhost:27017: connection closed (configured timeouts: socketTimeoutMS: 10000.0ms, connectTimeoutMS: 10000.0ms)')>]>
2025-06-11 11:09:03 | ERROR    | backend.app:lifespan:84 - Failed to initialize MongoDB database
2025-06-11 11:09:03 | INFO     | backend.app:lifespan:92 - Initializing taskiq...
2025-06-11 11:09:03 | INFO     | backend.tasks:init_taskiq:89 - Initializing taskiq...
2025-06-11 11:09:03 | SUCCESS  | backend.tasks:init_taskiq:93 - Taskiq initialized successfully
2025-06-11 11:09:03 | SUCCESS  | backend.app:lifespan:102 - Motion Agent API with Taskiq initialized successfully
2025-06-11 11:09:03 | INFO     | backend.app:lifespan:103 - Motion Agent API is ready to serve requests
2025-06-11 11:09:13 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 11:09:14 | INFO     | backend.routers.conversations:create_conversation:51 - Creating new conversation: New Conversation 6/11/2025, 11:09:14 AM
2025-06-11 11:09:14 | ERROR    | backend.services.conversation_service:create_conversation:59 - Failed to create conversation: 
2025-06-11 11:09:14 | ERROR    | backend.routers.conversations:create_conversation:59 - Failed to create conversation: 
2025-06-11 11:09:43 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 11:10:13 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 11:10:43 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 11:11:13 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 11:11:43 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 11:12:43 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 11:13:43 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 11:14:43 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 11:15:43 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 11:16:43 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 11:17:43 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 11:18:43 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 11:19:43 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 11:20:43 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 11:21:43 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 11:22:43 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 11:23:43 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 11:24:43 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 11:25:43 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 11:26:43 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 11:27:43 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 11:28:43 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 11:29:43 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 11:30:43 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 11:31:43 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 11:32:43 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 11:33:43 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 11:34:53 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 11:35:53 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 11:36:53 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 11:48:23 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 11:49:23 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 11:49:44 | INFO     | backend.app:lifespan:107 - Shutting down Motion Agent API...
2025-06-11 11:49:44 | INFO     | backend.tasks:shutdown_taskiq:104 - Shutting down taskiq...
2025-06-11 11:49:44 | SUCCESS  | backend.tasks:shutdown_taskiq:106 - Taskiq shutdown successfully
2025-06-11 11:49:44 | INFO     | backend.database:close_database:94 - Closing MongoDB connections...
2025-06-11 11:49:44 | SUCCESS  | backend.database:close_database:101 - MongoDB connections closed
2025-06-11 11:49:44 | SUCCESS  | backend.app:lifespan:115 - Motion Agent API shutdown completed
2025-06-11 11:49:46 | SUCCESS  | backend.config:validate_config:309 - Configuration validation passed
2025-06-11 11:49:46 | INFO     | backend.app:lifespan:78 - Starting Motion Agent API server with Taskiq...
2025-06-11 11:49:46 | INFO     | backend.app:lifespan:81 - Initializing MongoDB database...
2025-06-11 11:49:46 | INFO     | backend.database:init_database:50 - Initializing MongoDB database...
2025-06-11 11:49:51 | ERROR    | backend.database:init_database:85 - Failed to initialize MongoDB database: localhost:27017: connection closed (configured timeouts: socketTimeoutMS: 10000.0ms, connectTimeoutMS: 10000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 6848fcdaeee1f09752f215c0, topology_type: Unknown, servers: [<ServerDescription ('localhost', 27017) server_type: Unknown, rtt: None, error=AutoReconnect('localhost:27017: connection closed (configured timeouts: socketTimeoutMS: 10000.0ms, connectTimeoutMS: 10000.0ms)')>]>
2025-06-11 11:49:51 | ERROR    | backend.app:lifespan:84 - Failed to initialize MongoDB database
2025-06-11 11:49:51 | INFO     | backend.app:lifespan:92 - Initializing taskiq...
2025-06-11 11:49:51 | INFO     | backend.tasks:init_taskiq:89 - Initializing taskiq...
2025-06-11 11:49:51 | SUCCESS  | backend.tasks:init_taskiq:93 - Taskiq initialized successfully
2025-06-11 11:49:51 | SUCCESS  | backend.app:lifespan:102 - Motion Agent API with Taskiq initialized successfully
2025-06-11 11:49:51 | INFO     | backend.app:lifespan:103 - Motion Agent API is ready to serve requests
2025-06-11 11:50:23 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 11:52:03 | INFO     | backend.app:lifespan:107 - Shutting down Motion Agent API...
2025-06-11 11:52:03 | INFO     | backend.tasks:shutdown_taskiq:104 - Shutting down taskiq...
2025-06-11 11:52:03 | SUCCESS  | backend.tasks:shutdown_taskiq:106 - Taskiq shutdown successfully
2025-06-11 11:52:03 | INFO     | backend.database:close_database:94 - Closing MongoDB connections...
2025-06-11 11:52:03 | SUCCESS  | backend.database:close_database:101 - MongoDB connections closed
2025-06-11 11:52:03 | SUCCESS  | backend.app:lifespan:115 - Motion Agent API shutdown completed
2025-06-11 11:52:52 | SUCCESS  | backend.config:validate_config:309 - Configuration validation passed
2025-06-11 11:52:52 | INFO     | backend.app:lifespan:78 - Starting Motion Agent API server with Taskiq...
2025-06-11 11:52:52 | INFO     | backend.app:lifespan:81 - Initializing MongoDB database...
2025-06-11 11:52:52 | INFO     | backend.database:init_database:50 - Initializing MongoDB database...
2025-06-11 11:52:52 | SUCCESS  | backend.database:init_database:81 - MongoDB database initialized successfully: motion_agent
2025-06-11 11:52:52 | INFO     | backend.app:lifespan:88 - Creating database indexes...
2025-06-11 11:52:52 | INFO     | backend.database:create_indexes:259 - Creating database indexes...
2025-06-11 11:52:52 | SUCCESS  | backend.database:create_indexes:279 - Database indexes created successfully
2025-06-11 11:52:52 | INFO     | backend.app:lifespan:92 - Initializing taskiq...
2025-06-11 11:52:52 | INFO     | backend.tasks:init_taskiq:89 - Initializing taskiq...
2025-06-11 11:52:52 | SUCCESS  | backend.tasks:init_taskiq:93 - Taskiq initialized successfully
2025-06-11 11:52:52 | SUCCESS  | backend.app:lifespan:102 - Motion Agent API with Taskiq initialized successfully
2025-06-11 11:52:52 | INFO     | backend.app:lifespan:103 - Motion Agent API is ready to serve requests
2025-06-11 11:53:04 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 11:53:04 | INFO     | backend.routers.conversations:list_conversations:107 - Listed 1 conversations
2025-06-11 11:53:04 | INFO     | backend.routers.conversations:create_conversation:51 - Creating new conversation: New Conversation 6/11/2025, 11:53:04 AM
2025-06-11 11:53:04 | INFO     | backend.services.conversation_service:create_conversation:54 - Created new conversation: 6848fda0204a226de4b926fa
2025-06-11 11:53:04 | SUCCESS  | backend.routers.conversations:create_conversation:55 - Created conversation: 6848fda0204a226de4b926fa
2025-06-11 11:53:28 | INFO     | backend.routers.conversations:create_conversation:51 - Creating new conversation: New Conversation 6/11/2025, 11:53:28 AM
2025-06-11 11:53:28 | INFO     | backend.services.conversation_service:create_conversation:54 - Created new conversation: 6848fdb8204a226de4b926fb
2025-06-11 11:53:28 | SUCCESS  | backend.routers.conversations:create_conversation:55 - Created conversation: 6848fdb8204a226de4b926fb
2025-06-11 11:53:34 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 11:53:57 | ERROR    | backend.routers.conversations:send_message:303 - Failed to send message to conversation 6848fdb8204a226de4b926fb: 'TaskService' object has no attribute 'submit_conversation_task'
2025-06-11 11:54:05 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 11:54:16 | INFO     | backend.routers.animation:generate_professional_animation:108 - Received professional animation request: 向后10步跑 然后转身
2025-06-11 11:54:16 | INFO     | backend.services.task_service:create_task:69 - Created task: e8ec6ee1-375c-4f51-ab97-353cb58eb821 (animation_generation)
2025-06-11 11:54:16 | INFO     | backend.services.task_service:create_animation_task:223 - Created animation task: e8ec6ee1-375c-4f51-ab97-353cb58eb821
2025-06-11 11:54:16 | INFO     | backend.routers.animation:generate_professional_animation:127 - Created animation task: e8ec6ee1-375c-4f51-ab97-353cb58eb821
2025-06-11 11:54:16 | INFO     | backend.services.task_service:update_task_status:262 - Updated task e8ec6ee1-375c-4f51-ab97-353cb58eb821 status to running
2025-06-11 11:54:21 | WARNING  | backend.animation.professional_nlu:_load_spacy_model:55 - No spaCy model found, using blank model
2025-06-11 11:54:21 | INFO     | backend.animation.professional_nlu:_load_sentiment_model:64 - Using rule-based sentiment analysis instead of large model
2025-06-11 11:54:21 | INFO     | backend.animation.professional_nlu:_load_action_classifier:74 - Using rule-based action classification instead of large model
2025-06-11 11:54:21 | INFO     | backend.animation.professional_nlu:__init__:41 - Professional Animator NLU initialized
2025-06-11 11:54:21 | INFO     | backend.animation.animator_functions:__init__:37 - Unified Professional Animator initialized
2025-06-11 11:54:21 | INFO     | backend.animation.professional_pipeline:__init__:84 - Professional Animation Pipeline initialized with enhanced features
2025-06-11 11:54:21 | WARNING  | backend.animation.professional_nlu:_load_spacy_model:55 - No spaCy model found, using blank model
2025-06-11 11:54:21 | INFO     | backend.animation.professional_nlu:_load_sentiment_model:64 - Using rule-based sentiment analysis instead of large model
2025-06-11 11:54:21 | INFO     | backend.animation.professional_nlu:_load_action_classifier:74 - Using rule-based action classification instead of large model
2025-06-11 11:54:21 | INFO     | backend.animation.professional_nlu:__init__:41 - Professional Animator NLU initialized
2025-06-11 11:54:21 | INFO     | backend.animation.animator_functions:__init__:37 - Unified Professional Animator initialized
2025-06-11 11:54:21 | INFO     | backend.animation.professional_pipeline:__init__:84 - Professional Animation Pipeline initialized with enhanced features
2025-06-11 11:54:21 | INFO     | backend.animation.professional_pipeline:process_animation_request:93 - Processing animation request: 向后10步跑 然后转身
2025-06-11 11:54:21 | INFO     | backend.animation.professional_pipeline:process_animation_request:97 - Step 1: Natural Language Understanding
2025-06-11 11:54:21 | INFO     | backend.animation.professional_nlu:process_natural_language:397 - Processing animation request: 向后10步跑 然后转身
2025-06-11 11:54:21 | SUCCESS  | backend.animation.professional_nlu:process_natural_language:426 - Successfully processed 2 actions
2025-06-11 11:54:21 | INFO     | backend.animation.professional_pipeline:process_animation_request:104 - Step 2: Animator Function Processing
2025-06-11 11:54:21 | INFO     | backend.animation.professional_pipeline:_apply_animator_functions:179 - Applying unified animator functions with enhanced features
2025-06-11 11:54:21 | INFO     | backend.animation.animator_functions:create_run_cycle:106 - Creating run cycle: Direction.BACKWARD, AnimationIntensity.FAST, steps=0
2025-06-11 11:54:21 | INFO     | backend.animation.animator_functions:create_complex_turn:610 - Creating complex turn: 180 degrees, 0 steps
2025-06-11 11:54:21 | INFO     | backend.animation.professional_pipeline:_apply_animator_functions:190 - Optimizing action transitions
2025-06-11 11:54:21 | INFO     | backend.animation.transition_optimizer:optimize_action_sequence:146 - Optimizing sequence of 2 actions...
2025-06-11 11:54:21 | SUCCESS  | backend.animation.transition_optimizer:optimize_action_sequence:165 - Optimized sequence now has 17 frames
2025-06-11 11:54:21 | ERROR    | backend.animation.professional_pipeline:process_animation_request:165 - Error in animation pipeline: 1 validation error for AnimatorAction
type
  Input should be 'locomotion', 'idle', 'combat_attack', 'combat_defend', 'combat_hit', 'combat_death', 'acrobatic', 'parkour', 'interaction', 'gesture', 'emote', 'transition' or 'blend' [type=enum, input_value='transition_anticipation', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/enum
2025-06-11 11:54:21 | INFO     | backend.services.task_service:update_task_status:262 - Updated task e8ec6ee1-375c-4f51-ab97-353cb58eb821 status to failed
2025-06-11 11:54:21 | INFO     | backend.routers.animation:generate_professional_animation:205 - Updated task e8ec6ee1-375c-4f51-ab97-353cb58eb821 status to failed
2025-06-11 11:54:21 | SUCCESS  | backend.routers.animation:generate_professional_animation:209 - Professional animation generation completed
2025-06-11 11:54:35 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 11:55:05 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 11:55:42 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 11:56:12 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 12:13:48 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 12:14:48 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 12:15:48 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 12:16:48 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 12:17:48 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 12:18:48 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 12:19:48 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 12:20:48 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 12:21:48 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 12:22:48 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 12:23:48 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 12:24:48 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 12:25:48 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 12:26:48 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 12:27:48 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 12:28:48 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 12:29:48 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 12:30:48 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 12:31:48 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 12:32:48 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 12:33:48 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 12:34:48 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 12:35:48 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 12:36:48 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 12:37:48 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 12:38:48 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 12:39:48 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 12:40:48 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 12:41:48 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 12:42:48 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 12:43:48 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 12:44:48 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 12:45:48 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 12:46:48 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 12:47:48 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 12:48:48 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 12:49:48 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 12:50:48 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 12:51:48 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 12:52:48 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 12:53:48 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 12:54:48 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 12:55:48 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 12:56:48 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 12:57:48 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 12:58:48 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 12:59:48 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 13:00:48 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 13:01:48 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 13:02:48 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 13:03:48 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 13:04:48 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 13:05:12 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 13:05:21 | INFO     | backend.routers.animation:generate_professional_animation:108 - Received professional animation request: 向后10步跑 然后转身
2025-06-11 13:05:21 | INFO     | backend.services.task_service:create_task:69 - Created task: 3522d91a-9a3a-4909-b248-742fc93efa7a (animation_generation)
2025-06-11 13:05:21 | INFO     | backend.services.task_service:create_animation_task:223 - Created animation task: 3522d91a-9a3a-4909-b248-742fc93efa7a
2025-06-11 13:05:21 | INFO     | backend.routers.animation:generate_professional_animation:127 - Created animation task: 3522d91a-9a3a-4909-b248-742fc93efa7a
2025-06-11 13:05:21 | INFO     | backend.services.task_service:update_task_status:262 - Updated task 3522d91a-9a3a-4909-b248-742fc93efa7a status to running
2025-06-11 13:05:21 | WARNING  | backend.animation.professional_nlu:_load_spacy_model:55 - No spaCy model found, using blank model
2025-06-11 13:05:21 | INFO     | backend.animation.professional_nlu:_load_sentiment_model:64 - Using rule-based sentiment analysis instead of large model
2025-06-11 13:05:21 | INFO     | backend.animation.professional_nlu:_load_action_classifier:74 - Using rule-based action classification instead of large model
2025-06-11 13:05:21 | INFO     | backend.animation.professional_nlu:__init__:41 - Professional Animator NLU initialized
2025-06-11 13:05:21 | INFO     | backend.animation.animator_functions:__init__:37 - Unified Professional Animator initialized
2025-06-11 13:05:21 | INFO     | backend.animation.professional_pipeline:__init__:84 - Professional Animation Pipeline initialized with enhanced features
2025-06-11 13:05:21 | INFO     | backend.animation.professional_pipeline:process_animation_request:93 - Processing animation request: 向后10步跑 然后转身
2025-06-11 13:05:21 | INFO     | backend.animation.professional_pipeline:process_animation_request:97 - Step 1: Natural Language Understanding
2025-06-11 13:05:21 | INFO     | backend.animation.professional_nlu:process_natural_language:397 - Processing animation request: 向后10步跑 然后转身
2025-06-11 13:05:21 | SUCCESS  | backend.animation.professional_nlu:process_natural_language:426 - Successfully processed 2 actions
2025-06-11 13:05:21 | INFO     | backend.animation.professional_pipeline:process_animation_request:104 - Step 2: Animator Function Processing
2025-06-11 13:05:21 | INFO     | backend.animation.professional_pipeline:_apply_animator_functions:179 - Applying unified animator functions with enhanced features
2025-06-11 13:05:21 | INFO     | backend.animation.animator_functions:create_run_cycle:106 - Creating run cycle: Direction.BACKWARD, AnimationIntensity.FAST, steps=0
2025-06-11 13:05:21 | INFO     | backend.animation.animator_functions:create_complex_turn:610 - Creating complex turn: 180 degrees, 0 steps
2025-06-11 13:05:21 | INFO     | backend.animation.professional_pipeline:_apply_animator_functions:190 - Optimizing action transitions
2025-06-11 13:05:21 | INFO     | backend.animation.transition_optimizer:optimize_action_sequence:146 - Optimizing sequence of 2 actions...
2025-06-11 13:05:21 | SUCCESS  | backend.animation.transition_optimizer:optimize_action_sequence:165 - Optimized sequence now has 17 frames
2025-06-11 13:05:21 | ERROR    | backend.animation.professional_pipeline:process_animation_request:165 - Error in animation pipeline: 1 validation error for AnimatorAction
type
  Input should be 'locomotion', 'idle', 'combat_attack', 'combat_defend', 'combat_hit', 'combat_death', 'acrobatic', 'parkour', 'interaction', 'gesture', 'emote', 'transition' or 'blend' [type=enum, input_value='transition_anticipation', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/enum
2025-06-11 13:05:21 | INFO     | backend.services.task_service:update_task_status:262 - Updated task 3522d91a-9a3a-4909-b248-742fc93efa7a status to failed
2025-06-11 13:05:21 | INFO     | backend.routers.animation:generate_professional_animation:205 - Updated task 3522d91a-9a3a-4909-b248-742fc93efa7a status to failed
2025-06-11 13:05:21 | SUCCESS  | backend.routers.animation:generate_professional_animation:209 - Professional animation generation completed
2025-06-11 13:05:30 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 13:06:00 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 13:06:30 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 13:06:44 | INFO     | backend.app:lifespan:107 - Shutting down Motion Agent API...
2025-06-11 13:06:44 | INFO     | backend.tasks:shutdown_taskiq:104 - Shutting down taskiq...
2025-06-11 13:06:44 | SUCCESS  | backend.tasks:shutdown_taskiq:106 - Taskiq shutdown successfully
2025-06-11 13:06:44 | INFO     | backend.database:close_database:94 - Closing MongoDB connections...
2025-06-11 13:06:44 | SUCCESS  | backend.database:close_database:101 - MongoDB connections closed
2025-06-11 13:06:44 | SUCCESS  | backend.app:lifespan:115 - Motion Agent API shutdown completed
2025-06-11 13:06:45 | SUCCESS  | backend.config:validate_config:309 - Configuration validation passed
2025-06-11 13:06:45 | INFO     | backend.app:lifespan:78 - Starting Motion Agent API server with Taskiq...
2025-06-11 13:06:45 | INFO     | backend.app:lifespan:81 - Initializing MongoDB database...
2025-06-11 13:06:45 | INFO     | backend.database:init_database:50 - Initializing MongoDB database...
2025-06-11 13:06:45 | SUCCESS  | backend.database:init_database:81 - MongoDB database initialized successfully: motion_agent
2025-06-11 13:06:45 | INFO     | backend.app:lifespan:88 - Creating database indexes...
2025-06-11 13:06:45 | INFO     | backend.database:create_indexes:259 - Creating database indexes...
2025-06-11 13:06:45 | SUCCESS  | backend.database:create_indexes:279 - Database indexes created successfully
2025-06-11 13:06:45 | INFO     | backend.app:lifespan:92 - Initializing taskiq...
2025-06-11 13:06:45 | INFO     | backend.tasks:init_taskiq:89 - Initializing taskiq...
2025-06-11 13:06:45 | SUCCESS  | backend.tasks:init_taskiq:93 - Taskiq initialized successfully
2025-06-11 13:06:45 | SUCCESS  | backend.app:lifespan:102 - Motion Agent API with Taskiq initialized successfully
2025-06-11 13:06:45 | INFO     | backend.app:lifespan:103 - Motion Agent API is ready to serve requests
2025-06-11 13:06:59 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 13:07:07 | INFO     | backend.app:lifespan:107 - Shutting down Motion Agent API...
2025-06-11 13:07:07 | INFO     | backend.tasks:shutdown_taskiq:104 - Shutting down taskiq...
2025-06-11 13:07:07 | SUCCESS  | backend.tasks:shutdown_taskiq:106 - Taskiq shutdown successfully
2025-06-11 13:07:07 | INFO     | backend.database:close_database:94 - Closing MongoDB connections...
2025-06-11 13:07:07 | SUCCESS  | backend.database:close_database:101 - MongoDB connections closed
2025-06-11 13:07:07 | SUCCESS  | backend.app:lifespan:115 - Motion Agent API shutdown completed
2025-06-11 13:07:07 | SUCCESS  | backend.config:validate_config:309 - Configuration validation passed
2025-06-11 13:07:07 | INFO     | backend.app:lifespan:78 - Starting Motion Agent API server with Taskiq...
2025-06-11 13:07:07 | INFO     | backend.app:lifespan:81 - Initializing MongoDB database...
2025-06-11 13:07:07 | INFO     | backend.database:init_database:50 - Initializing MongoDB database...
2025-06-11 13:07:07 | SUCCESS  | backend.database:init_database:81 - MongoDB database initialized successfully: motion_agent
2025-06-11 13:07:07 | INFO     | backend.app:lifespan:88 - Creating database indexes...
2025-06-11 13:07:07 | INFO     | backend.database:create_indexes:259 - Creating database indexes...
2025-06-11 13:07:07 | SUCCESS  | backend.database:create_indexes:279 - Database indexes created successfully
2025-06-11 13:07:07 | INFO     | backend.app:lifespan:92 - Initializing taskiq...
2025-06-11 13:07:07 | INFO     | backend.tasks:init_taskiq:89 - Initializing taskiq...
2025-06-11 13:07:07 | SUCCESS  | backend.tasks:init_taskiq:93 - Taskiq initialized successfully
2025-06-11 13:07:07 | SUCCESS  | backend.app:lifespan:102 - Motion Agent API with Taskiq initialized successfully
2025-06-11 13:07:07 | INFO     | backend.app:lifespan:103 - Motion Agent API is ready to serve requests
2025-06-11 13:07:21 | INFO     | backend.app:lifespan:107 - Shutting down Motion Agent API...
2025-06-11 13:07:21 | INFO     | backend.tasks:shutdown_taskiq:104 - Shutting down taskiq...
2025-06-11 13:07:21 | SUCCESS  | backend.tasks:shutdown_taskiq:106 - Taskiq shutdown successfully
2025-06-11 13:07:21 | INFO     | backend.database:close_database:94 - Closing MongoDB connections...
2025-06-11 13:07:21 | SUCCESS  | backend.database:close_database:101 - MongoDB connections closed
2025-06-11 13:07:21 | SUCCESS  | backend.app:lifespan:115 - Motion Agent API shutdown completed
2025-06-11 13:07:21 | SUCCESS  | backend.config:validate_config:309 - Configuration validation passed
2025-06-11 13:07:21 | INFO     | backend.app:lifespan:78 - Starting Motion Agent API server with Taskiq...
2025-06-11 13:07:21 | INFO     | backend.app:lifespan:81 - Initializing MongoDB database...
2025-06-11 13:07:21 | INFO     | backend.database:init_database:50 - Initializing MongoDB database...
2025-06-11 13:07:21 | SUCCESS  | backend.database:init_database:81 - MongoDB database initialized successfully: motion_agent
2025-06-11 13:07:21 | INFO     | backend.app:lifespan:88 - Creating database indexes...
2025-06-11 13:07:21 | INFO     | backend.database:create_indexes:259 - Creating database indexes...
2025-06-11 13:07:21 | SUCCESS  | backend.database:create_indexes:279 - Database indexes created successfully
2025-06-11 13:07:21 | INFO     | backend.app:lifespan:92 - Initializing taskiq...
2025-06-11 13:07:21 | INFO     | backend.tasks:init_taskiq:89 - Initializing taskiq...
2025-06-11 13:07:21 | SUCCESS  | backend.tasks:init_taskiq:93 - Taskiq initialized successfully
2025-06-11 13:07:21 | SUCCESS  | backend.app:lifespan:102 - Motion Agent API with Taskiq initialized successfully
2025-06-11 13:07:21 | INFO     | backend.app:lifespan:103 - Motion Agent API is ready to serve requests
2025-06-11 13:07:43 | INFO     | backend.app:lifespan:107 - Shutting down Motion Agent API...
2025-06-11 13:07:43 | INFO     | backend.tasks:shutdown_taskiq:104 - Shutting down taskiq...
2025-06-11 13:07:43 | SUCCESS  | backend.tasks:shutdown_taskiq:106 - Taskiq shutdown successfully
2025-06-11 13:07:43 | INFO     | backend.database:close_database:94 - Closing MongoDB connections...
2025-06-11 13:07:43 | SUCCESS  | backend.database:close_database:101 - MongoDB connections closed
2025-06-11 13:07:43 | SUCCESS  | backend.app:lifespan:115 - Motion Agent API shutdown completed
2025-06-11 13:07:43 | SUCCESS  | backend.config:validate_config:309 - Configuration validation passed
2025-06-11 13:07:43 | INFO     | backend.app:lifespan:78 - Starting Motion Agent API server with Taskiq...
2025-06-11 13:07:43 | INFO     | backend.app:lifespan:81 - Initializing MongoDB database...
2025-06-11 13:07:43 | INFO     | backend.database:init_database:50 - Initializing MongoDB database...
2025-06-11 13:07:43 | SUCCESS  | backend.database:init_database:81 - MongoDB database initialized successfully: motion_agent
2025-06-11 13:07:43 | INFO     | backend.app:lifespan:88 - Creating database indexes...
2025-06-11 13:07:43 | INFO     | backend.database:create_indexes:259 - Creating database indexes...
2025-06-11 13:07:43 | SUCCESS  | backend.database:create_indexes:279 - Database indexes created successfully
2025-06-11 13:07:43 | INFO     | backend.app:lifespan:92 - Initializing taskiq...
2025-06-11 13:07:43 | INFO     | backend.tasks:init_taskiq:89 - Initializing taskiq...
2025-06-11 13:07:43 | SUCCESS  | backend.tasks:init_taskiq:93 - Taskiq initialized successfully
2025-06-11 13:07:43 | SUCCESS  | backend.app:lifespan:102 - Motion Agent API with Taskiq initialized successfully
2025-06-11 13:07:43 | INFO     | backend.app:lifespan:103 - Motion Agent API is ready to serve requests
2025-06-11 13:07:48 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 13:07:58 | INFO     | backend.app:lifespan:107 - Shutting down Motion Agent API...
2025-06-11 13:07:58 | INFO     | backend.tasks:shutdown_taskiq:104 - Shutting down taskiq...
2025-06-11 13:07:58 | SUCCESS  | backend.tasks:shutdown_taskiq:106 - Taskiq shutdown successfully
2025-06-11 13:07:58 | INFO     | backend.database:close_database:94 - Closing MongoDB connections...
2025-06-11 13:07:58 | SUCCESS  | backend.database:close_database:101 - MongoDB connections closed
2025-06-11 13:07:58 | SUCCESS  | backend.app:lifespan:115 - Motion Agent API shutdown completed
2025-06-11 13:07:59 | SUCCESS  | backend.config:validate_config:309 - Configuration validation passed
2025-06-11 13:07:59 | INFO     | backend.app:lifespan:78 - Starting Motion Agent API server with Taskiq...
2025-06-11 13:07:59 | INFO     | backend.app:lifespan:81 - Initializing MongoDB database...
2025-06-11 13:07:59 | INFO     | backend.database:init_database:50 - Initializing MongoDB database...
2025-06-11 13:07:59 | SUCCESS  | backend.database:init_database:81 - MongoDB database initialized successfully: motion_agent
2025-06-11 13:07:59 | INFO     | backend.app:lifespan:88 - Creating database indexes...
2025-06-11 13:07:59 | INFO     | backend.database:create_indexes:259 - Creating database indexes...
2025-06-11 13:07:59 | SUCCESS  | backend.database:create_indexes:279 - Database indexes created successfully
2025-06-11 13:07:59 | INFO     | backend.app:lifespan:92 - Initializing taskiq...
2025-06-11 13:07:59 | INFO     | backend.tasks:init_taskiq:89 - Initializing taskiq...
2025-06-11 13:07:59 | SUCCESS  | backend.tasks:init_taskiq:93 - Taskiq initialized successfully
2025-06-11 13:07:59 | SUCCESS  | backend.app:lifespan:102 - Motion Agent API with Taskiq initialized successfully
2025-06-11 13:07:59 | INFO     | backend.app:lifespan:103 - Motion Agent API is ready to serve requests
2025-06-11 13:08:34 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 13:08:59 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 13:10:40 | SUCCESS  | backend.config:validate_config:309 - Configuration validation passed
2025-06-11 13:10:40 | INFO     | backend.app:lifespan:78 - Starting Motion Agent API server with Taskiq...
2025-06-11 13:10:40 | INFO     | backend.app:lifespan:81 - Initializing MongoDB database...
2025-06-11 13:10:40 | INFO     | backend.database:init_database:50 - Initializing MongoDB database...
2025-06-11 13:10:40 | SUCCESS  | backend.database:init_database:81 - MongoDB database initialized successfully: motion_agent
2025-06-11 13:10:40 | INFO     | backend.app:lifespan:88 - Creating database indexes...
2025-06-11 13:10:40 | INFO     | backend.database:create_indexes:259 - Creating database indexes...
2025-06-11 13:10:40 | SUCCESS  | backend.database:create_indexes:279 - Database indexes created successfully
2025-06-11 13:10:40 | INFO     | backend.app:lifespan:92 - Initializing taskiq...
2025-06-11 13:10:40 | INFO     | backend.tasks:init_taskiq:89 - Initializing taskiq...
2025-06-11 13:10:40 | SUCCESS  | backend.tasks:init_taskiq:93 - Taskiq initialized successfully
2025-06-11 13:10:40 | SUCCESS  | backend.app:lifespan:102 - Motion Agent API with Taskiq initialized successfully
2025-06-11 13:10:40 | INFO     | backend.app:lifespan:103 - Motion Agent API is ready to serve requests
2025-06-11 13:10:44 | ERROR    | backend.routers.conversations:send_message:303 - Failed to send message to conversation 6848fdb8204a226de4b926fb: 'TaskService' object has no attribute 'submit_conversation_task'
2025-06-11 13:11:00 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 13:11:25 | INFO     | backend.routers.animation:generate_professional_animation:108 - Received professional animation request: 向后10步跑 然后转身
2025-06-11 13:11:25 | INFO     | backend.services.task_service:create_task:69 - Created task: ab193c0c-4049-407d-9694-4fbb6e7737ed (animation_generation)
2025-06-11 13:11:25 | INFO     | backend.services.task_service:create_animation_task:223 - Created animation task: ab193c0c-4049-407d-9694-4fbb6e7737ed
2025-06-11 13:11:25 | INFO     | backend.routers.animation:generate_professional_animation:127 - Created animation task: ab193c0c-4049-407d-9694-4fbb6e7737ed
2025-06-11 13:11:25 | INFO     | backend.services.task_service:update_task_status:262 - Updated task ab193c0c-4049-407d-9694-4fbb6e7737ed status to running
2025-06-11 13:11:26 | WARNING  | backend.animation.professional_nlu:_load_spacy_model:55 - No spaCy model found, using blank model
2025-06-11 13:11:26 | INFO     | backend.animation.professional_nlu:_load_sentiment_model:64 - Using rule-based sentiment analysis instead of large model
2025-06-11 13:11:26 | INFO     | backend.animation.professional_nlu:_load_action_classifier:74 - Using rule-based action classification instead of large model
2025-06-11 13:11:26 | INFO     | backend.animation.professional_nlu:__init__:41 - Professional Animator NLU initialized
2025-06-11 13:11:26 | INFO     | backend.animation.animator_functions:__init__:37 - Unified Professional Animator initialized
2025-06-11 13:11:26 | INFO     | backend.animation.professional_pipeline:__init__:85 - Professional Animation Pipeline initialized with enhanced features
2025-06-11 13:11:26 | WARNING  | backend.animation.professional_nlu:_load_spacy_model:55 - No spaCy model found, using blank model
2025-06-11 13:11:26 | INFO     | backend.animation.professional_nlu:_load_sentiment_model:64 - Using rule-based sentiment analysis instead of large model
2025-06-11 13:11:26 | INFO     | backend.animation.professional_nlu:_load_action_classifier:74 - Using rule-based action classification instead of large model
2025-06-11 13:11:26 | INFO     | backend.animation.professional_nlu:__init__:41 - Professional Animator NLU initialized
2025-06-11 13:11:26 | INFO     | backend.animation.animator_functions:__init__:37 - Unified Professional Animator initialized
2025-06-11 13:11:26 | INFO     | backend.animation.professional_pipeline:__init__:85 - Professional Animation Pipeline initialized with enhanced features
2025-06-11 13:11:26 | INFO     | backend.animation.professional_pipeline:process_animation_request:94 - Processing animation request: 向后10步跑 然后转身
2025-06-11 13:11:26 | INFO     | backend.animation.professional_pipeline:process_animation_request:98 - Step 1: Natural Language Understanding
2025-06-11 13:11:26 | INFO     | backend.animation.professional_nlu:process_natural_language:397 - Processing animation request: 向后10步跑 然后转身
2025-06-11 13:11:26 | SUCCESS  | backend.animation.professional_nlu:process_natural_language:426 - Successfully processed 2 actions
2025-06-11 13:11:26 | INFO     | backend.animation.professional_pipeline:process_animation_request:105 - Step 2: Animator Function Processing
2025-06-11 13:11:26 | INFO     | backend.animation.professional_pipeline:_apply_animator_functions:180 - Applying unified animator functions with enhanced features
2025-06-11 13:11:26 | INFO     | backend.animation.animator_functions:create_run_cycle:106 - Creating run cycle: Direction.BACKWARD, AnimationIntensity.FAST, steps=0
2025-06-11 13:11:26 | INFO     | backend.animation.animator_functions:create_complex_turn:610 - Creating complex turn: 180 degrees, 0 steps
2025-06-11 13:11:26 | INFO     | backend.animation.professional_pipeline:_apply_animator_functions:191 - Optimizing action transitions
2025-06-11 13:11:26 | INFO     | backend.animation.transition_optimizer:optimize_action_sequence:146 - Optimizing sequence of 2 actions...
2025-06-11 13:11:26 | SUCCESS  | backend.animation.transition_optimizer:optimize_action_sequence:165 - Optimized sequence now has 17 frames
2025-06-11 13:11:26 | INFO     | backend.animation.professional_pipeline:process_animation_request:111 - Step 3: Generate Blender Animation Data
2025-06-11 13:11:26 | INFO     | backend.animation.professional_pipeline:process_animation_request:115 - Step 4: Execute Blender Animation Generation
2025-06-11 13:11:26 | INFO     | backend.animation.professional_pipeline:_execute_blender_generation:382 - Executing Blender command: /Applications/Blender.app/Contents/MacOS/Blender --background --python blender_scripts/generate_animation.py -- --input temp/animation_data/animation_data_1749618686.json --output output/animations/animation_default_1749618686.fbx --format fbx
2025-06-11 13:11:26 | SUCCESS  | backend.animation.professional_pipeline:_execute_blender_generation:394 - Blender animation generated: output/animations/animation_default_1749618686.fbx
2025-06-11 13:11:26 | WARNING  | backend.animation.professional_pipeline:process_animation_request:120 - Blender generation failed, creating test FBX file
2025-06-11 13:11:26 | INFO     | backend.animation.professional_pipeline:_create_test_fbx_file:579 - Created test FBX file: output/animations/animation_1749618686.fbx
2025-06-11 13:11:26 | INFO     | backend.animation.professional_pipeline:process_animation_request:126 - Step 5: FBX File Validation
2025-06-11 13:11:26 | INFO     | backend.animation.fbx_validator:validate_fbx_file:26 - Starting FBX validation for: output/animations/animation_1749618686.fbx
2025-06-11 13:11:26 | SUCCESS  | backend.animation.fbx_validator:validate_fbx_file:85 - FBX validation completed for: output/animations/animation_1749618686.fbx
2025-06-11 13:11:26 | INFO     | backend.animation.professional_pipeline:process_animation_request:135 - Step 6: Animation Quality Check
2025-06-11 13:11:26 | INFO     | backend.animation.quality_checker:check_animation_quality:130 - Starting animation quality check...
2025-06-11 13:11:26 | SUCCESS  | backend.animation.quality_checker:check_animation_quality:171 - Quality check completed. Overall score: 0.68
2025-06-11 13:11:26 | SUCCESS  | backend.animation.professional_pipeline:process_animation_request:160 - Animation generated successfully in 0.40s
2025-06-11 13:11:26 | INFO     | backend.services.task_service:update_task_status:262 - Updated task ab193c0c-4049-407d-9694-4fbb6e7737ed status to completed
2025-06-11 13:11:26 | INFO     | backend.routers.animation:generate_professional_animation:205 - Updated task ab193c0c-4049-407d-9694-4fbb6e7737ed status to completed
2025-06-11 13:11:26 | SUCCESS  | backend.routers.animation:generate_professional_animation:209 - Professional animation generation completed
2025-06-11 13:11:29 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 13:11:44 | INFO     | backend.app:lifespan:107 - Shutting down Motion Agent API...
2025-06-11 13:11:44 | INFO     | backend.tasks:shutdown_taskiq:104 - Shutting down taskiq...
2025-06-11 13:11:44 | SUCCESS  | backend.tasks:shutdown_taskiq:106 - Taskiq shutdown successfully
2025-06-11 13:11:44 | INFO     | backend.database:close_database:94 - Closing MongoDB connections...
2025-06-11 13:11:44 | SUCCESS  | backend.database:close_database:101 - MongoDB connections closed
2025-06-11 13:11:44 | SUCCESS  | backend.app:lifespan:115 - Motion Agent API shutdown completed
2025-06-11 13:11:44 | SUCCESS  | backend.config:validate_config:309 - Configuration validation passed
2025-06-11 13:11:44 | INFO     | backend.app:lifespan:78 - Starting Motion Agent API server with Taskiq...
2025-06-11 13:11:44 | INFO     | backend.app:lifespan:81 - Initializing MongoDB database...
2025-06-11 13:11:44 | INFO     | backend.database:init_database:50 - Initializing MongoDB database...
2025-06-11 13:11:44 | SUCCESS  | backend.database:init_database:81 - MongoDB database initialized successfully: motion_agent
2025-06-11 13:11:44 | INFO     | backend.app:lifespan:88 - Creating database indexes...
2025-06-11 13:11:44 | INFO     | backend.database:create_indexes:259 - Creating database indexes...
2025-06-11 13:11:44 | SUCCESS  | backend.database:create_indexes:279 - Database indexes created successfully
2025-06-11 13:11:44 | INFO     | backend.app:lifespan:92 - Initializing taskiq...
2025-06-11 13:11:44 | INFO     | backend.tasks:init_taskiq:89 - Initializing taskiq...
2025-06-11 13:11:44 | SUCCESS  | backend.tasks:init_taskiq:93 - Taskiq initialized successfully
2025-06-11 13:11:44 | SUCCESS  | backend.app:lifespan:102 - Motion Agent API with Taskiq initialized successfully
2025-06-11 13:11:44 | INFO     | backend.app:lifespan:103 - Motion Agent API is ready to serve requests
2025-06-11 13:11:59 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 13:12:06 | INFO     | backend.app:lifespan:107 - Shutting down Motion Agent API...
2025-06-11 13:12:06 | INFO     | backend.tasks:shutdown_taskiq:104 - Shutting down taskiq...
2025-06-11 13:12:06 | SUCCESS  | backend.tasks:shutdown_taskiq:106 - Taskiq shutdown successfully
2025-06-11 13:12:06 | INFO     | backend.database:close_database:94 - Closing MongoDB connections...
2025-06-11 13:12:06 | SUCCESS  | backend.database:close_database:101 - MongoDB connections closed
2025-06-11 13:12:06 | SUCCESS  | backend.app:lifespan:115 - Motion Agent API shutdown completed
2025-06-11 13:12:06 | SUCCESS  | backend.config:validate_config:309 - Configuration validation passed
2025-06-11 13:12:06 | INFO     | backend.app:lifespan:78 - Starting Motion Agent API server with Taskiq...
2025-06-11 13:12:06 | INFO     | backend.app:lifespan:81 - Initializing MongoDB database...
2025-06-11 13:12:06 | INFO     | backend.database:init_database:50 - Initializing MongoDB database...
2025-06-11 13:12:07 | SUCCESS  | backend.database:init_database:81 - MongoDB database initialized successfully: motion_agent
2025-06-11 13:12:07 | INFO     | backend.app:lifespan:88 - Creating database indexes...
2025-06-11 13:12:07 | INFO     | backend.database:create_indexes:259 - Creating database indexes...
2025-06-11 13:12:07 | SUCCESS  | backend.database:create_indexes:279 - Database indexes created successfully
2025-06-11 13:12:07 | INFO     | backend.app:lifespan:92 - Initializing taskiq...
2025-06-11 13:12:07 | INFO     | backend.tasks:init_taskiq:89 - Initializing taskiq...
2025-06-11 13:12:07 | SUCCESS  | backend.tasks:init_taskiq:93 - Taskiq initialized successfully
2025-06-11 13:12:07 | SUCCESS  | backend.app:lifespan:102 - Motion Agent API with Taskiq initialized successfully
2025-06-11 13:12:07 | INFO     | backend.app:lifespan:103 - Motion Agent API is ready to serve requests
2025-06-11 13:12:29 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 13:14:37 | SUCCESS  | backend.config:validate_config:309 - Configuration validation passed
2025-06-11 13:14:37 | INFO     | backend.app:lifespan:78 - Starting Motion Agent API server with Taskiq...
2025-06-11 13:14:37 | INFO     | backend.app:lifespan:81 - Initializing MongoDB database...
2025-06-11 13:14:37 | INFO     | backend.database:init_database:50 - Initializing MongoDB database...
2025-06-11 13:14:37 | SUCCESS  | backend.database:init_database:81 - MongoDB database initialized successfully: motion_agent
2025-06-11 13:14:37 | INFO     | backend.app:lifespan:88 - Creating database indexes...
2025-06-11 13:14:37 | INFO     | backend.database:create_indexes:259 - Creating database indexes...
2025-06-11 13:14:37 | SUCCESS  | backend.database:create_indexes:279 - Database indexes created successfully
2025-06-11 13:14:37 | INFO     | backend.app:lifespan:92 - Initializing taskiq...
2025-06-11 13:14:37 | INFO     | backend.tasks:init_taskiq:89 - Initializing taskiq...
2025-06-11 13:14:37 | SUCCESS  | backend.tasks:init_taskiq:93 - Taskiq initialized successfully
2025-06-11 13:14:37 | SUCCESS  | backend.app:lifespan:102 - Motion Agent API with Taskiq initialized successfully
2025-06-11 13:14:37 | INFO     | backend.app:lifespan:103 - Motion Agent API is ready to serve requests
2025-06-11 13:14:41 | INFO     | backend.services.task_service:create_task:69 - Created task: 545daa4d-ac29-4dcd-9a2e-68c0df717448 (conversation_processing)
2025-06-11 13:14:41 | INFO     | backend.services.task_service:submit_conversation_task:259 - Created conversation task: 545daa4d-ac29-4dcd-9a2e-68c0df717448
2025-06-11 13:14:41 | INFO     | backend.routers.conversations:send_message:291 - Submitted message processing task: 545daa4d-ac29-4dcd-9a2e-68c0df717448
2025-06-11 13:14:46 | INFO     | backend.routers.conversations:list_conversations:107 - Listed 3 conversations
2025-06-11 13:14:46 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 13:14:46 | INFO     | backend.routers.conversations:create_conversation:51 - Creating new conversation: New Conversation 6/11/2025, 1:14:46 PM
2025-06-11 13:14:46 | INFO     | backend.services.conversation_service:create_conversation:54 - Created new conversation: 684910c6245862c204f7ba0a
2025-06-11 13:14:46 | SUCCESS  | backend.routers.conversations:create_conversation:55 - Created conversation: 684910c6245862c204f7ba0a
2025-06-11 13:14:49 | INFO     | backend.services.task_service:create_task:69 - Created task: a01c5407-9933-43a1-8d1c-056d2cc1acc8 (conversation_processing)
2025-06-11 13:14:49 | INFO     | backend.services.task_service:submit_conversation_task:259 - Created conversation task: a01c5407-9933-43a1-8d1c-056d2cc1acc8
2025-06-11 13:14:49 | INFO     | backend.routers.conversations:send_message:291 - Submitted message processing task: a01c5407-9933-43a1-8d1c-056d2cc1acc8
2025-06-11 13:15:05 | INFO     | backend.routers.conversations:create_conversation:51 - Creating new conversation: New Conversation 6/11/2025, 1:15:05 PM
2025-06-11 13:15:05 | INFO     | backend.services.conversation_service:create_conversation:54 - Created new conversation: 684910d9245862c204f7ba0c
2025-06-11 13:15:05 | SUCCESS  | backend.routers.conversations:create_conversation:55 - Created conversation: 684910d9245862c204f7ba0c
2025-06-11 13:15:07 | INFO     | backend.routers.conversations:create_conversation:51 - Creating new conversation: New Conversation 6/11/2025, 1:15:07 PM
2025-06-11 13:15:07 | INFO     | backend.services.conversation_service:create_conversation:54 - Created new conversation: 684910db245862c204f7ba0d
2025-06-11 13:15:07 | SUCCESS  | backend.routers.conversations:create_conversation:55 - Created conversation: 684910db245862c204f7ba0d
2025-06-11 13:15:08 | INFO     | backend.routers.conversations:create_conversation:51 - Creating new conversation: New Conversation 6/11/2025, 1:15:08 PM
2025-06-11 13:15:08 | INFO     | backend.services.conversation_service:create_conversation:54 - Created new conversation: 684910dc245862c204f7ba0e
2025-06-11 13:15:08 | SUCCESS  | backend.routers.conversations:create_conversation:55 - Created conversation: 684910dc245862c204f7ba0e
2025-06-11 13:15:08 | INFO     | backend.routers.conversations:create_conversation:51 - Creating new conversation: New Conversation 6/11/2025, 1:15:08 PM
2025-06-11 13:15:08 | INFO     | backend.services.conversation_service:create_conversation:54 - Created new conversation: 684910dc245862c204f7ba0f
2025-06-11 13:15:08 | SUCCESS  | backend.routers.conversations:create_conversation:55 - Created conversation: 684910dc245862c204f7ba0f
2025-06-11 13:15:17 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 13:15:47 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 13:16:17 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 13:16:45 | INFO     | backend.services.task_service:create_task:69 - Created task: 37a81e12-07b8-4756-940c-50d35d7c70f2 (conversation_processing)
2025-06-11 13:16:45 | INFO     | backend.services.task_service:submit_conversation_task:259 - Created conversation task: 37a81e12-07b8-4756-940c-50d35d7c70f2
2025-06-11 13:16:45 | INFO     | backend.routers.conversations:send_message:291 - Submitted message processing task: 37a81e12-07b8-4756-940c-50d35d7c70f2
2025-06-11 13:16:46 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 13:17:14 | INFO     | backend.services.task_service:create_task:69 - Created task: 1f2b7fa1-00fe-40fc-bcb5-e659c211313f (conversation_processing)
2025-06-11 13:17:14 | INFO     | backend.services.task_service:submit_conversation_task:259 - Created conversation task: 1f2b7fa1-00fe-40fc-bcb5-e659c211313f
2025-06-11 13:17:14 | INFO     | backend.routers.conversations:send_message:291 - Submitted message processing task: 1f2b7fa1-00fe-40fc-bcb5-e659c211313f
2025-06-11 13:17:16 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 13:17:47 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 13:18:17 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 13:18:34 | INFO     | backend.app:lifespan:107 - Shutting down Motion Agent API...
2025-06-11 13:18:34 | INFO     | backend.tasks:shutdown_taskiq:104 - Shutting down taskiq...
2025-06-11 13:18:34 | SUCCESS  | backend.tasks:shutdown_taskiq:106 - Taskiq shutdown successfully
2025-06-11 13:18:34 | INFO     | backend.database:close_database:94 - Closing MongoDB connections...
2025-06-11 13:18:34 | SUCCESS  | backend.database:close_database:101 - MongoDB connections closed
2025-06-11 13:18:34 | SUCCESS  | backend.app:lifespan:115 - Motion Agent API shutdown completed
2025-06-11 13:18:35 | SUCCESS  | backend.config:validate_config:309 - Configuration validation passed
2025-06-11 13:18:35 | INFO     | backend.app:lifespan:78 - Starting Motion Agent API server with Taskiq...
2025-06-11 13:18:35 | INFO     | backend.app:lifespan:81 - Initializing MongoDB database...
2025-06-11 13:18:35 | INFO     | backend.database:init_database:50 - Initializing MongoDB database...
2025-06-11 13:18:35 | SUCCESS  | backend.database:init_database:81 - MongoDB database initialized successfully: motion_agent
2025-06-11 13:18:35 | INFO     | backend.app:lifespan:88 - Creating database indexes...
2025-06-11 13:18:35 | INFO     | backend.database:create_indexes:259 - Creating database indexes...
2025-06-11 13:18:35 | SUCCESS  | backend.database:create_indexes:279 - Database indexes created successfully
2025-06-11 13:18:35 | INFO     | backend.app:lifespan:92 - Initializing taskiq...
2025-06-11 13:18:35 | INFO     | backend.tasks:init_taskiq:89 - Initializing taskiq...
2025-06-11 13:18:35 | SUCCESS  | backend.tasks:init_taskiq:93 - Taskiq initialized successfully
2025-06-11 13:18:35 | SUCCESS  | backend.app:lifespan:102 - Motion Agent API with Taskiq initialized successfully
2025-06-11 13:18:35 | INFO     | backend.app:lifespan:103 - Motion Agent API is ready to serve requests
2025-06-11 13:18:47 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 13:18:53 | INFO     | backend.app:lifespan:107 - Shutting down Motion Agent API...
2025-06-11 13:18:53 | INFO     | backend.tasks:shutdown_taskiq:104 - Shutting down taskiq...
2025-06-11 13:18:53 | SUCCESS  | backend.tasks:shutdown_taskiq:106 - Taskiq shutdown successfully
2025-06-11 13:18:53 | INFO     | backend.database:close_database:94 - Closing MongoDB connections...
2025-06-11 13:18:53 | SUCCESS  | backend.database:close_database:101 - MongoDB connections closed
2025-06-11 13:18:53 | SUCCESS  | backend.app:lifespan:115 - Motion Agent API shutdown completed
2025-06-11 13:18:53 | SUCCESS  | backend.config:validate_config:309 - Configuration validation passed
2025-06-11 13:18:53 | INFO     | backend.app:lifespan:78 - Starting Motion Agent API server with Taskiq...
2025-06-11 13:18:53 | INFO     | backend.app:lifespan:81 - Initializing MongoDB database...
2025-06-11 13:18:53 | INFO     | backend.database:init_database:50 - Initializing MongoDB database...
2025-06-11 13:18:53 | SUCCESS  | backend.database:init_database:81 - MongoDB database initialized successfully: motion_agent
2025-06-11 13:18:53 | INFO     | backend.app:lifespan:88 - Creating database indexes...
2025-06-11 13:18:53 | INFO     | backend.database:create_indexes:259 - Creating database indexes...
2025-06-11 13:18:53 | SUCCESS  | backend.database:create_indexes:279 - Database indexes created successfully
2025-06-11 13:18:53 | INFO     | backend.app:lifespan:92 - Initializing taskiq...
2025-06-11 13:18:53 | INFO     | backend.tasks:init_taskiq:89 - Initializing taskiq...
2025-06-11 13:18:53 | SUCCESS  | backend.tasks:init_taskiq:93 - Taskiq initialized successfully
2025-06-11 13:18:53 | SUCCESS  | backend.app:lifespan:102 - Motion Agent API with Taskiq initialized successfully
2025-06-11 13:18:53 | INFO     | backend.app:lifespan:103 - Motion Agent API is ready to serve requests
2025-06-11 13:19:17 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 13:19:47 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 13:42:47 | SUCCESS  | backend.config:validate_config:309 - Configuration validation passed
2025-06-11 13:42:47 | INFO     | backend.app:lifespan:78 - Starting Motion Agent API server with Taskiq...
2025-06-11 13:42:47 | INFO     | backend.app:lifespan:81 - Initializing MongoDB database...
2025-06-11 13:42:47 | INFO     | backend.database:init_database:50 - Initializing MongoDB database...
2025-06-11 13:42:47 | SUCCESS  | backend.database:init_database:81 - MongoDB database initialized successfully: motion_agent
2025-06-11 13:42:47 | INFO     | backend.app:lifespan:88 - Creating database indexes...
2025-06-11 13:42:47 | INFO     | backend.database:create_indexes:259 - Creating database indexes...
2025-06-11 13:42:47 | SUCCESS  | backend.database:create_indexes:279 - Database indexes created successfully
2025-06-11 13:42:47 | INFO     | backend.app:lifespan:92 - Initializing taskiq...
2025-06-11 13:42:47 | INFO     | backend.tasks:init_taskiq:89 - Initializing taskiq...
2025-06-11 13:42:47 | SUCCESS  | backend.tasks:init_taskiq:93 - Taskiq initialized successfully
2025-06-11 13:42:47 | SUCCESS  | backend.app:lifespan:102 - Motion Agent API with Taskiq initialized successfully
2025-06-11 13:42:47 | INFO     | backend.app:lifespan:103 - Motion Agent API is ready to serve requests
2025-06-11 13:42:59 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 13:43:29 | INFO     | backend.services.task_service:create_task:69 - Created task: 59a41a29-dfe4-473c-8608-7484ff930b71 (conversation_processing)
2025-06-11 13:43:29 | INFO     | backend.services.task_service:submit_conversation_task:259 - Created conversation task: 59a41a29-dfe4-473c-8608-7484ff930b71
2025-06-11 13:43:29 | INFO     | backend.services.task_service:submit_conversation_task:274 - Submitted conversation task to queue: 59a41a29-dfe4-473c-8608-7484ff930b71
2025-06-11 13:43:29 | INFO     | backend.routers.conversations:send_message:291 - Submitted message processing task: 59a41a29-dfe4-473c-8608-7484ff930b71
2025-06-11 13:43:29 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 13:44:17 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 13:45:17 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 13:45:29 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 13:45:42 | INFO     | backend.app:lifespan:107 - Shutting down Motion Agent API...
2025-06-11 13:45:42 | INFO     | backend.tasks:shutdown_taskiq:104 - Shutting down taskiq...
2025-06-11 13:45:42 | SUCCESS  | backend.tasks:shutdown_taskiq:106 - Taskiq shutdown successfully
2025-06-11 13:45:42 | INFO     | backend.database:close_database:94 - Closing MongoDB connections...
2025-06-11 13:45:42 | SUCCESS  | backend.database:close_database:101 - MongoDB connections closed
2025-06-11 13:45:42 | SUCCESS  | backend.app:lifespan:115 - Motion Agent API shutdown completed
2025-06-11 13:45:42 | SUCCESS  | backend.config:validate_config:309 - Configuration validation passed
2025-06-11 13:45:42 | INFO     | backend.app:lifespan:78 - Starting Motion Agent API server with Taskiq...
2025-06-11 13:45:42 | INFO     | backend.app:lifespan:81 - Initializing MongoDB database...
2025-06-11 13:45:42 | INFO     | backend.database:init_database:50 - Initializing MongoDB database...
2025-06-11 13:45:42 | SUCCESS  | backend.database:init_database:81 - MongoDB database initialized successfully: motion_agent
2025-06-11 13:45:42 | INFO     | backend.app:lifespan:88 - Creating database indexes...
2025-06-11 13:45:42 | INFO     | backend.database:create_indexes:259 - Creating database indexes...
2025-06-11 13:45:42 | SUCCESS  | backend.database:create_indexes:279 - Database indexes created successfully
2025-06-11 13:45:42 | INFO     | backend.app:lifespan:92 - Initializing taskiq...
2025-06-11 13:45:42 | INFO     | backend.tasks:init_taskiq:89 - Initializing taskiq...
2025-06-11 13:45:42 | SUCCESS  | backend.tasks:init_taskiq:93 - Taskiq initialized successfully
2025-06-11 13:45:42 | SUCCESS  | backend.app:lifespan:102 - Motion Agent API with Taskiq initialized successfully
2025-06-11 13:45:42 | INFO     | backend.app:lifespan:103 - Motion Agent API is ready to serve requests
2025-06-11 13:45:59 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 13:46:02 | INFO     | backend.app:lifespan:107 - Shutting down Motion Agent API...
2025-06-11 13:46:02 | INFO     | backend.tasks:shutdown_taskiq:104 - Shutting down taskiq...
2025-06-11 13:46:02 | SUCCESS  | backend.tasks:shutdown_taskiq:106 - Taskiq shutdown successfully
2025-06-11 13:46:02 | INFO     | backend.database:close_database:94 - Closing MongoDB connections...
2025-06-11 13:46:02 | SUCCESS  | backend.database:close_database:101 - MongoDB connections closed
2025-06-11 13:46:02 | SUCCESS  | backend.app:lifespan:115 - Motion Agent API shutdown completed
2025-06-11 13:46:02 | SUCCESS  | backend.config:validate_config:309 - Configuration validation passed
2025-06-11 13:46:02 | INFO     | backend.app:lifespan:78 - Starting Motion Agent API server with Taskiq...
2025-06-11 13:46:02 | INFO     | backend.app:lifespan:81 - Initializing MongoDB database...
2025-06-11 13:46:02 | INFO     | backend.database:init_database:50 - Initializing MongoDB database...
2025-06-11 13:46:03 | SUCCESS  | backend.database:init_database:81 - MongoDB database initialized successfully: motion_agent
2025-06-11 13:46:03 | INFO     | backend.app:lifespan:88 - Creating database indexes...
2025-06-11 13:46:03 | INFO     | backend.database:create_indexes:259 - Creating database indexes...
2025-06-11 13:46:03 | SUCCESS  | backend.database:create_indexes:279 - Database indexes created successfully
2025-06-11 13:46:03 | INFO     | backend.app:lifespan:92 - Initializing taskiq...
2025-06-11 13:46:03 | INFO     | backend.tasks:init_taskiq:89 - Initializing taskiq...
2025-06-11 13:46:03 | SUCCESS  | backend.tasks:init_taskiq:93 - Taskiq initialized successfully
2025-06-11 13:46:03 | SUCCESS  | backend.app:lifespan:102 - Motion Agent API with Taskiq initialized successfully
2025-06-11 13:46:03 | INFO     | backend.app:lifespan:103 - Motion Agent API is ready to serve requests
2025-06-11 13:46:19 | INFO     | backend.app:lifespan:107 - Shutting down Motion Agent API...
2025-06-11 13:46:19 | INFO     | backend.tasks:shutdown_taskiq:104 - Shutting down taskiq...
2025-06-11 13:46:19 | SUCCESS  | backend.tasks:shutdown_taskiq:106 - Taskiq shutdown successfully
2025-06-11 13:46:19 | INFO     | backend.database:close_database:94 - Closing MongoDB connections...
2025-06-11 13:46:19 | SUCCESS  | backend.database:close_database:101 - MongoDB connections closed
2025-06-11 13:46:19 | SUCCESS  | backend.app:lifespan:115 - Motion Agent API shutdown completed
2025-06-11 13:46:19 | SUCCESS  | backend.config:validate_config:309 - Configuration validation passed
2025-06-11 13:46:19 | INFO     | backend.app:lifespan:78 - Starting Motion Agent API server with Taskiq...
2025-06-11 13:46:19 | INFO     | backend.app:lifespan:81 - Initializing MongoDB database...
2025-06-11 13:46:19 | INFO     | backend.database:init_database:50 - Initializing MongoDB database...
2025-06-11 13:46:19 | SUCCESS  | backend.database:init_database:81 - MongoDB database initialized successfully: motion_agent
2025-06-11 13:46:19 | INFO     | backend.app:lifespan:88 - Creating database indexes...
2025-06-11 13:46:19 | INFO     | backend.database:create_indexes:259 - Creating database indexes...
2025-06-11 13:46:19 | SUCCESS  | backend.database:create_indexes:279 - Database indexes created successfully
2025-06-11 13:46:19 | INFO     | backend.app:lifespan:92 - Initializing taskiq...
2025-06-11 13:46:19 | INFO     | backend.tasks:init_taskiq:89 - Initializing taskiq...
2025-06-11 13:46:19 | SUCCESS  | backend.tasks:init_taskiq:93 - Taskiq initialized successfully
2025-06-11 13:46:19 | SUCCESS  | backend.app:lifespan:102 - Motion Agent API with Taskiq initialized successfully
2025-06-11 13:46:19 | INFO     | backend.app:lifespan:103 - Motion Agent API is ready to serve requests
2025-06-11 13:46:29 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 13:46:59 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 13:47:01 | INFO     | backend.services.task_service:create_task:69 - Created task: c2a896ac-26c8-44b9-8460-68c6e312b3f5 (conversation_processing)
2025-06-11 13:47:01 | INFO     | backend.services.task_service:submit_conversation_task:259 - Created conversation task: c2a896ac-26c8-44b9-8460-68c6e312b3f5
2025-06-11 13:47:01 | INFO     | backend.services.task_service:submit_conversation_task:274 - Submitted conversation task to queue: c2a896ac-26c8-44b9-8460-68c6e312b3f5
2025-06-11 13:47:01 | INFO     | backend.routers.conversations:send_message:291 - Submitted message processing task: c2a896ac-26c8-44b9-8460-68c6e312b3f5
2025-06-11 13:47:29 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 13:47:59 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 13:48:11 | INFO     | backend.app:lifespan:107 - Shutting down Motion Agent API...
2025-06-11 13:48:11 | INFO     | backend.tasks:shutdown_taskiq:104 - Shutting down taskiq...
2025-06-11 13:48:11 | SUCCESS  | backend.tasks:shutdown_taskiq:106 - Taskiq shutdown successfully
2025-06-11 13:48:11 | INFO     | backend.database:close_database:94 - Closing MongoDB connections...
2025-06-11 13:48:11 | SUCCESS  | backend.database:close_database:101 - MongoDB connections closed
2025-06-11 13:48:11 | SUCCESS  | backend.app:lifespan:115 - Motion Agent API shutdown completed
2025-06-11 13:48:11 | SUCCESS  | backend.config:validate_config:309 - Configuration validation passed
2025-06-11 13:48:12 | INFO     | backend.app:lifespan:78 - Starting Motion Agent API server with Taskiq...
2025-06-11 13:48:12 | INFO     | backend.app:lifespan:81 - Initializing MongoDB database...
2025-06-11 13:48:12 | INFO     | backend.database:init_database:50 - Initializing MongoDB database...
2025-06-11 13:48:12 | SUCCESS  | backend.database:init_database:81 - MongoDB database initialized successfully: motion_agent
2025-06-11 13:48:12 | INFO     | backend.app:lifespan:88 - Creating database indexes...
2025-06-11 13:48:12 | INFO     | backend.database:create_indexes:259 - Creating database indexes...
2025-06-11 13:48:12 | SUCCESS  | backend.database:create_indexes:279 - Database indexes created successfully
2025-06-11 13:48:12 | INFO     | backend.app:lifespan:92 - Initializing taskiq...
2025-06-11 13:48:12 | INFO     | backend.tasks:init_taskiq:89 - Initializing taskiq...
2025-06-11 13:48:12 | SUCCESS  | backend.tasks:init_taskiq:93 - Taskiq initialized successfully
2025-06-11 13:48:12 | SUCCESS  | backend.app:lifespan:102 - Motion Agent API with Taskiq initialized successfully
2025-06-11 13:48:12 | INFO     | backend.app:lifespan:103 - Motion Agent API is ready to serve requests
2025-06-11 13:48:31 | INFO     | backend.app:lifespan:107 - Shutting down Motion Agent API...
2025-06-11 13:48:31 | INFO     | backend.tasks:shutdown_taskiq:104 - Shutting down taskiq...
2025-06-11 13:48:31 | SUCCESS  | backend.tasks:shutdown_taskiq:106 - Taskiq shutdown successfully
2025-06-11 13:48:31 | INFO     | backend.database:close_database:94 - Closing MongoDB connections...
2025-06-11 13:48:31 | SUCCESS  | backend.database:close_database:101 - MongoDB connections closed
2025-06-11 13:48:31 | SUCCESS  | backend.app:lifespan:115 - Motion Agent API shutdown completed
2025-06-11 13:48:32 | SUCCESS  | backend.config:validate_config:309 - Configuration validation passed
2025-06-11 13:48:32 | INFO     | backend.app:lifespan:78 - Starting Motion Agent API server with Taskiq...
2025-06-11 13:48:32 | INFO     | backend.app:lifespan:81 - Initializing MongoDB database...
2025-06-11 13:48:32 | INFO     | backend.database:init_database:50 - Initializing MongoDB database...
2025-06-11 13:48:32 | SUCCESS  | backend.database:init_database:81 - MongoDB database initialized successfully: motion_agent
2025-06-11 13:48:32 | INFO     | backend.app:lifespan:88 - Creating database indexes...
2025-06-11 13:48:32 | INFO     | backend.database:create_indexes:259 - Creating database indexes...
2025-06-11 13:48:32 | SUCCESS  | backend.database:create_indexes:279 - Database indexes created successfully
2025-06-11 13:48:32 | INFO     | backend.app:lifespan:92 - Initializing taskiq...
2025-06-11 13:48:32 | INFO     | backend.tasks:init_taskiq:89 - Initializing taskiq...
2025-06-11 13:48:32 | SUCCESS  | backend.tasks:init_taskiq:93 - Taskiq initialized successfully
2025-06-11 13:48:32 | SUCCESS  | backend.app:lifespan:102 - Motion Agent API with Taskiq initialized successfully
2025-06-11 13:48:32 | INFO     | backend.app:lifespan:103 - Motion Agent API is ready to serve requests
2025-06-11 13:48:48 | INFO     | backend.app:lifespan:107 - Shutting down Motion Agent API...
2025-06-11 13:48:48 | INFO     | backend.tasks:shutdown_taskiq:104 - Shutting down taskiq...
2025-06-11 13:48:48 | SUCCESS  | backend.tasks:shutdown_taskiq:106 - Taskiq shutdown successfully
2025-06-11 13:48:48 | INFO     | backend.database:close_database:94 - Closing MongoDB connections...
2025-06-11 13:48:48 | SUCCESS  | backend.database:close_database:101 - MongoDB connections closed
2025-06-11 13:48:48 | SUCCESS  | backend.app:lifespan:115 - Motion Agent API shutdown completed
2025-06-11 13:48:49 | SUCCESS  | backend.config:validate_config:309 - Configuration validation passed
2025-06-11 13:48:49 | INFO     | backend.app:lifespan:78 - Starting Motion Agent API server with Taskiq...
2025-06-11 13:48:49 | INFO     | backend.app:lifespan:81 - Initializing MongoDB database...
2025-06-11 13:48:49 | INFO     | backend.database:init_database:50 - Initializing MongoDB database...
2025-06-11 13:48:49 | SUCCESS  | backend.database:init_database:81 - MongoDB database initialized successfully: motion_agent
2025-06-11 13:48:49 | INFO     | backend.app:lifespan:88 - Creating database indexes...
2025-06-11 13:48:49 | INFO     | backend.database:create_indexes:259 - Creating database indexes...
2025-06-11 13:48:49 | SUCCESS  | backend.database:create_indexes:279 - Database indexes created successfully
2025-06-11 13:48:49 | INFO     | backend.app:lifespan:92 - Initializing taskiq...
2025-06-11 13:48:49 | INFO     | backend.tasks:init_taskiq:89 - Initializing taskiq...
2025-06-11 13:48:49 | SUCCESS  | backend.tasks:init_taskiq:93 - Taskiq initialized successfully
2025-06-11 13:48:49 | SUCCESS  | backend.app:lifespan:102 - Motion Agent API with Taskiq initialized successfully
2025-06-11 13:48:49 | INFO     | backend.app:lifespan:103 - Motion Agent API is ready to serve requests
2025-06-11 13:49:17 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 13:49:25 | INFO     | backend.services.task_service:create_task:69 - Created task: 6f7058d7-4009-4fe7-bb16-481ad7c47dbf (conversation_processing)
2025-06-11 13:49:25 | INFO     | backend.services.task_service:submit_conversation_task:259 - Created conversation task: 6f7058d7-4009-4fe7-bb16-481ad7c47dbf
2025-06-11 13:49:25 | INFO     | backend.services.task_service:submit_conversation_task:274 - Submitted conversation task to queue: 6f7058d7-4009-4fe7-bb16-481ad7c47dbf
2025-06-11 13:49:25 | INFO     | backend.routers.conversations:send_message:291 - Submitted message processing task: 6f7058d7-4009-4fe7-bb16-481ad7c47dbf
2025-06-11 13:49:58 | INFO     | backend.app:lifespan:107 - Shutting down Motion Agent API...
2025-06-11 13:49:58 | INFO     | backend.tasks:shutdown_taskiq:104 - Shutting down taskiq...
2025-06-11 13:49:58 | SUCCESS  | backend.tasks:shutdown_taskiq:106 - Taskiq shutdown successfully
2025-06-11 13:49:58 | INFO     | backend.database:close_database:94 - Closing MongoDB connections...
2025-06-11 13:49:58 | SUCCESS  | backend.database:close_database:101 - MongoDB connections closed
2025-06-11 13:49:58 | SUCCESS  | backend.app:lifespan:115 - Motion Agent API shutdown completed
2025-06-11 13:49:58 | SUCCESS  | backend.config:validate_config:309 - Configuration validation passed
2025-06-11 13:49:58 | INFO     | backend.app:lifespan:78 - Starting Motion Agent API server with Taskiq...
2025-06-11 13:49:58 | INFO     | backend.app:lifespan:81 - Initializing MongoDB database...
2025-06-11 13:49:58 | INFO     | backend.database:init_database:50 - Initializing MongoDB database...
2025-06-11 13:49:58 | SUCCESS  | backend.database:init_database:81 - MongoDB database initialized successfully: motion_agent
2025-06-11 13:49:58 | INFO     | backend.app:lifespan:88 - Creating database indexes...
2025-06-11 13:49:58 | INFO     | backend.database:create_indexes:259 - Creating database indexes...
2025-06-11 13:49:58 | SUCCESS  | backend.database:create_indexes:279 - Database indexes created successfully
2025-06-11 13:49:58 | INFO     | backend.app:lifespan:92 - Initializing taskiq...
2025-06-11 13:49:58 | INFO     | backend.tasks:init_taskiq:89 - Initializing taskiq...
2025-06-11 13:49:58 | SUCCESS  | backend.tasks:init_taskiq:93 - Taskiq initialized successfully
2025-06-11 13:49:58 | SUCCESS  | backend.app:lifespan:102 - Motion Agent API with Taskiq initialized successfully
2025-06-11 13:49:58 | INFO     | backend.app:lifespan:103 - Motion Agent API is ready to serve requests
2025-06-11 13:50:04 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 13:50:29 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 13:50:59 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 13:52:17 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 13:53:17 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 13:54:17 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 13:55:17 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 13:56:17 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 13:57:17 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 13:58:17 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 13:59:17 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 14:00:17 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 14:01:17 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 14:02:17 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 14:03:17 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 14:04:17 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 14:05:17 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 14:06:17 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 14:06:34 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 14:06:35 | INFO     | backend.services.task_service:create_task:69 - Created task: 36438304-b425-4eb9-9a81-823581fd32b7 (conversation_processing)
2025-06-11 14:06:35 | INFO     | backend.services.task_service:submit_conversation_task:259 - Created conversation task: 36438304-b425-4eb9-9a81-823581fd32b7
2025-06-11 14:06:35 | INFO     | backend.services.task_service:submit_conversation_task:274 - Submitted conversation task to queue: 36438304-b425-4eb9-9a81-823581fd32b7
2025-06-11 14:06:35 | INFO     | backend.routers.conversations:send_message:291 - Submitted message processing task: 36438304-b425-4eb9-9a81-823581fd32b7
2025-06-11 14:06:59 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 14:07:59 | SUCCESS  | backend.config:validate_config:309 - Configuration validation passed
2025-06-11 14:07:59 | INFO     | backend.app:lifespan:78 - Starting Motion Agent API server with Taskiq...
2025-06-11 14:07:59 | INFO     | backend.app:lifespan:81 - Initializing MongoDB database...
2025-06-11 14:07:59 | INFO     | backend.database:init_database:50 - Initializing MongoDB database...
2025-06-11 14:08:00 | SUCCESS  | backend.database:init_database:81 - MongoDB database initialized successfully: motion_agent
2025-06-11 14:08:00 | INFO     | backend.app:lifespan:88 - Creating database indexes...
2025-06-11 14:08:00 | INFO     | backend.database:create_indexes:259 - Creating database indexes...
2025-06-11 14:08:00 | SUCCESS  | backend.database:create_indexes:279 - Database indexes created successfully
2025-06-11 14:08:00 | INFO     | backend.app:lifespan:92 - Initializing taskiq...
2025-06-11 14:08:00 | INFO     | backend.tasks:init_taskiq:89 - Initializing taskiq...
2025-06-11 14:08:00 | SUCCESS  | backend.tasks:init_taskiq:93 - Taskiq initialized successfully
2025-06-11 14:08:00 | SUCCESS  | backend.app:lifespan:102 - Motion Agent API with Taskiq initialized successfully
2025-06-11 14:08:00 | INFO     | backend.app:lifespan:103 - Motion Agent API is ready to serve requests
2025-06-11 14:08:00 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 14:08:29 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 14:09:04 | INFO     | backend.app:lifespan:107 - Shutting down Motion Agent API...
2025-06-11 14:09:04 | INFO     | backend.tasks:shutdown_taskiq:104 - Shutting down taskiq...
2025-06-11 14:09:04 | SUCCESS  | backend.tasks:shutdown_taskiq:106 - Taskiq shutdown successfully
2025-06-11 14:09:04 | INFO     | backend.database:close_database:94 - Closing MongoDB connections...
2025-06-11 14:09:04 | SUCCESS  | backend.database:close_database:101 - MongoDB connections closed
2025-06-11 14:09:04 | SUCCESS  | backend.app:lifespan:115 - Motion Agent API shutdown completed
2025-06-11 14:09:05 | SUCCESS  | backend.config:validate_config:309 - Configuration validation passed
2025-06-11 14:09:05 | INFO     | backend.app:lifespan:78 - Starting Motion Agent API server with Taskiq...
2025-06-11 14:09:05 | INFO     | backend.app:lifespan:81 - Initializing MongoDB database...
2025-06-11 14:09:05 | INFO     | backend.database:init_database:50 - Initializing MongoDB database...
2025-06-11 14:09:05 | SUCCESS  | backend.database:init_database:81 - MongoDB database initialized successfully: motion_agent
2025-06-11 14:09:05 | INFO     | backend.app:lifespan:88 - Creating database indexes...
2025-06-11 14:09:05 | INFO     | backend.database:create_indexes:259 - Creating database indexes...
2025-06-11 14:09:05 | SUCCESS  | backend.database:create_indexes:279 - Database indexes created successfully
2025-06-11 14:09:05 | INFO     | backend.app:lifespan:92 - Initializing taskiq...
2025-06-11 14:09:05 | INFO     | backend.tasks:init_taskiq:89 - Initializing taskiq...
2025-06-11 14:09:05 | SUCCESS  | backend.tasks:init_taskiq:93 - Taskiq initialized successfully
2025-06-11 14:09:05 | SUCCESS  | backend.app:lifespan:102 - Motion Agent API with Taskiq initialized successfully
2025-06-11 14:09:05 | INFO     | backend.app:lifespan:103 - Motion Agent API is ready to serve requests
2025-06-11 14:09:17 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 14:10:13 | INFO     | backend.routers.conversations:list_conversations:107 - Listed 9 conversations
2025-06-11 14:10:17 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 14:10:20 | INFO     | backend.services.task_service:create_task:69 - Created task: 49027398-f32f-49a0-8afc-f8e46825535f (conversation_processing)
2025-06-11 14:10:20 | INFO     | backend.services.task_service:submit_conversation_task:259 - Created conversation task: 49027398-f32f-49a0-8afc-f8e46825535f
2025-06-11 14:10:20 | INFO     | backend.services.task_service:submit_conversation_task:274 - Submitted conversation task to queue: 49027398-f32f-49a0-8afc-f8e46825535f
2025-06-11 14:10:20 | INFO     | backend.routers.conversations:send_message:291 - Submitted message processing task: 49027398-f32f-49a0-8afc-f8e46825535f
2025-06-11 14:10:42 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 14:10:44 | INFO     | backend.routers.conversations:list_conversations:107 - Listed 9 conversations
2025-06-11 14:10:44 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 14:10:44 | INFO     | backend.routers.conversations:create_conversation:51 - Creating new conversation: New Conversation 6/11/2025, 2:10:44 PM
2025-06-11 14:10:44 | INFO     | backend.services.conversation_service:create_conversation:54 - Created new conversation: 68491de4d235de79c517e442
2025-06-11 14:10:44 | SUCCESS  | backend.routers.conversations:create_conversation:55 - Created conversation: 68491de4d235de79c517e442
2025-06-11 14:10:48 | INFO     | backend.services.task_service:create_task:69 - Created task: cd5645b4-2636-4c48-875b-7621081efeff (conversation_processing)
2025-06-11 14:10:48 | INFO     | backend.services.task_service:submit_conversation_task:259 - Created conversation task: cd5645b4-2636-4c48-875b-7621081efeff
2025-06-11 14:10:48 | INFO     | backend.services.task_service:submit_conversation_task:274 - Submitted conversation task to queue: cd5645b4-2636-4c48-875b-7621081efeff
2025-06-11 14:10:48 | INFO     | backend.routers.conversations:send_message:291 - Submitted message processing task: cd5645b4-2636-4c48-875b-7621081efeff
2025-06-11 14:11:07 | INFO     | backend.services.task_service:create_task:69 - Created task: cf9a2a61-08ba-4c78-b68f-9bebaa48dd97 (conversation_processing)
2025-06-11 14:11:07 | INFO     | backend.services.task_service:submit_conversation_task:259 - Created conversation task: cf9a2a61-08ba-4c78-b68f-9bebaa48dd97
2025-06-11 14:11:07 | INFO     | backend.services.task_service:submit_conversation_task:274 - Submitted conversation task to queue: cf9a2a61-08ba-4c78-b68f-9bebaa48dd97
2025-06-11 14:11:07 | INFO     | backend.routers.conversations:send_message:291 - Submitted message processing task: cf9a2a61-08ba-4c78-b68f-9bebaa48dd97
2025-06-11 14:11:14 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 14:11:28 | INFO     | backend.routers.animation:generate_professional_animation:108 - Received professional animation request: 生成一个后空翻720度之后，向前五步走，然后转身，并向左迈一步
2025-06-11 14:11:28 | INFO     | backend.services.task_service:create_task:69 - Created task: 3901ad53-d8cd-417f-89c7-45608a4b98be (animation_generation)
2025-06-11 14:11:28 | INFO     | backend.services.task_service:create_animation_task:223 - Created animation task: 3901ad53-d8cd-417f-89c7-45608a4b98be
2025-06-11 14:11:28 | INFO     | backend.routers.animation:generate_professional_animation:127 - Created animation task: 3901ad53-d8cd-417f-89c7-45608a4b98be
2025-06-11 14:11:28 | INFO     | backend.services.task_service:update_task_status:364 - Updated task 3901ad53-d8cd-417f-89c7-45608a4b98be status to running
2025-06-11 14:11:30 | WARNING  | backend.animation.professional_nlu:_load_spacy_model:55 - No spaCy model found, using blank model
2025-06-11 14:11:30 | INFO     | backend.animation.professional_nlu:_load_sentiment_model:64 - Using rule-based sentiment analysis instead of large model
2025-06-11 14:11:30 | INFO     | backend.animation.professional_nlu:_load_action_classifier:74 - Using rule-based action classification instead of large model
2025-06-11 14:11:30 | INFO     | backend.animation.professional_nlu:__init__:41 - Professional Animator NLU initialized
2025-06-11 14:11:30 | INFO     | backend.animation.animator_functions:__init__:37 - Unified Professional Animator initialized
2025-06-11 14:11:30 | INFO     | backend.animation.professional_pipeline:__init__:85 - Professional Animation Pipeline initialized with enhanced features
2025-06-11 14:11:30 | WARNING  | backend.animation.professional_nlu:_load_spacy_model:55 - No spaCy model found, using blank model
2025-06-11 14:11:30 | INFO     | backend.animation.professional_nlu:_load_sentiment_model:64 - Using rule-based sentiment analysis instead of large model
2025-06-11 14:11:30 | INFO     | backend.animation.professional_nlu:_load_action_classifier:74 - Using rule-based action classification instead of large model
2025-06-11 14:11:30 | INFO     | backend.animation.professional_nlu:__init__:41 - Professional Animator NLU initialized
2025-06-11 14:11:30 | INFO     | backend.animation.animator_functions:__init__:37 - Unified Professional Animator initialized
2025-06-11 14:11:30 | INFO     | backend.animation.professional_pipeline:__init__:85 - Professional Animation Pipeline initialized with enhanced features
2025-06-11 14:11:30 | INFO     | backend.animation.professional_pipeline:process_animation_request:94 - Processing animation request: 生成一个后空翻720度之后，向前五步走，然后转身，并向左迈一步
2025-06-11 14:11:30 | INFO     | backend.animation.professional_pipeline:process_animation_request:98 - Step 1: Natural Language Understanding
2025-06-11 14:11:30 | INFO     | backend.animation.professional_nlu:process_natural_language:397 - Processing animation request: 生成一个后空翻720度之后，向前五步走，然后转身，并向左迈一步
2025-06-11 14:11:30 | SUCCESS  | backend.animation.professional_nlu:process_natural_language:426 - Successfully processed 3 actions
2025-06-11 14:11:30 | INFO     | backend.animation.professional_pipeline:process_animation_request:105 - Step 2: Animator Function Processing
2025-06-11 14:11:30 | INFO     | backend.animation.professional_pipeline:_apply_animator_functions:180 - Applying unified animator functions with enhanced features
2025-06-11 14:11:30 | INFO     | backend.animation.animator_functions:create_walk_cycle:66 - Creating walk cycle: Direction.FORWARD, AnimationIntensity.NORMAL, steps=0
2025-06-11 14:11:30 | INFO     | backend.animation.animator_functions:create_complex_turn:610 - Creating complex turn: 180 degrees, 0 steps
2025-06-11 14:11:30 | INFO     | backend.animation.professional_pipeline:_apply_animator_functions:191 - Optimizing action transitions
2025-06-11 14:11:30 | INFO     | backend.animation.transition_optimizer:optimize_action_sequence:146 - Optimizing sequence of 3 actions...
2025-06-11 14:11:30 | SUCCESS  | backend.animation.transition_optimizer:optimize_action_sequence:165 - Optimized sequence now has 33 frames
2025-06-11 14:11:30 | INFO     | backend.animation.professional_pipeline:process_animation_request:111 - Step 3: Generate Blender Animation Data
2025-06-11 14:11:30 | INFO     | backend.animation.professional_pipeline:process_animation_request:115 - Step 4: Execute Blender Animation Generation
2025-06-11 14:11:30 | INFO     | backend.animation.professional_pipeline:_execute_blender_generation:382 - Executing Blender command: /Applications/Blender.app/Contents/MacOS/Blender --background --python blender_scripts/generate_animation.py -- --input temp/animation_data/animation_data_1749622290.json --output output/animations/animation_default_1749622290.fbx --format fbx
2025-06-11 14:11:31 | SUCCESS  | backend.animation.professional_pipeline:_execute_blender_generation:394 - Blender animation generated: output/animations/animation_default_1749622290.fbx
2025-06-11 14:11:31 | WARNING  | backend.animation.professional_pipeline:process_animation_request:120 - Blender generation failed, creating test FBX file
2025-06-11 14:11:31 | INFO     | backend.animation.professional_pipeline:_create_test_fbx_file:579 - Created test FBX file: output/animations/animation_1749622291.fbx
2025-06-11 14:11:31 | INFO     | backend.animation.professional_pipeline:process_animation_request:126 - Step 5: FBX File Validation
2025-06-11 14:11:31 | INFO     | backend.animation.fbx_validator:validate_fbx_file:26 - Starting FBX validation for: output/animations/animation_1749622291.fbx
2025-06-11 14:11:31 | SUCCESS  | backend.animation.fbx_validator:validate_fbx_file:85 - FBX validation completed for: output/animations/animation_1749622291.fbx
2025-06-11 14:11:31 | INFO     | backend.animation.professional_pipeline:process_animation_request:135 - Step 6: Animation Quality Check
2025-06-11 14:11:31 | INFO     | backend.animation.quality_checker:check_animation_quality:130 - Starting animation quality check...
2025-06-11 14:11:31 | SUCCESS  | backend.animation.quality_checker:check_animation_quality:171 - Quality check completed. Overall score: 0.68
2025-06-11 14:11:31 | SUCCESS  | backend.animation.professional_pipeline:process_animation_request:160 - Animation generated successfully in 1.12s
2025-06-11 14:11:31 | INFO     | backend.services.task_service:update_task_status:364 - Updated task 3901ad53-d8cd-417f-89c7-45608a4b98be status to completed
2025-06-11 14:11:31 | INFO     | backend.routers.animation:generate_professional_animation:205 - Updated task 3901ad53-d8cd-417f-89c7-45608a4b98be status to completed
2025-06-11 14:11:31 | SUCCESS  | backend.routers.animation:generate_professional_animation:209 - Professional animation generation completed
2025-06-11 14:11:40 | INFO     | backend.routers.animation:download_animation_file:249 - Download request for file: animation_1749622291.fbx
2025-06-11 14:11:40 | INFO     | backend.routers.animation:download_animation_file:264 - Serving file: animation_1749622291.fbx (4142 bytes)
2025-06-11 14:11:44 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 14:12:14 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 14:12:44 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 14:13:14 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 14:13:44 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 14:14:17 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 14:15:17 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 14:16:17 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 14:17:17 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 14:18:17 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 14:19:17 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 14:20:17 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 14:21:17 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 14:22:17 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 14:23:17 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 14:24:17 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 14:25:17 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 14:26:17 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 14:27:17 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 14:28:17 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 14:29:17 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 14:30:17 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 14:31:17 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 14:32:17 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 14:33:17 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 14:34:17 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 14:35:17 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 14:36:17 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 14:37:17 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 14:38:17 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 14:39:17 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 14:40:17 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 14:41:17 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
